"use strict";
import { Sequelize, Options, PoolOptions, Transaction, QueryOptions, ReplicationOptions } from "sequelize";
import config from "../config";
import { Lazy, lazy, measures } from "@skywind-group/sw-utils";

const db = config.db;
const dbPool: PoolOptions = {

    /**
     * Maximum connections of the pool
     */
    max: db.maxConnections,

    /**
     * The maximum time, in milliseconds, that a connection can be idle before being released.
     */
    idle: db.maxIdleTime,

};
const dbOptions: Options = {

    /**
     * The dialect of the database you are connecting to. One of mysql, postgres, sqlite, mariadb and mssql.
     */
    dialect: "postgres",

    /**
     * Default isolation level
     */
    isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,

    /**
     * The dialect specific options
     */
    dialectOptions: {},

    /**
     * The host of the relational database.
     */
    host: db.host,

    /**
     * The port of the relational database.
     */
    port: db.port,

    /**
     * Connection pool options
     */
    pool: dbPool,

    /**
     * A function that gets executed everytime Sequelize would log something.
     *
     * Defaults to console.log
     */
    logging: config.queryLogging
};

if (db.ssl.isEnabled) {
    dbOptions.dialectOptions["ssl"] = db.ssl;
}

const readReplicas = config.dbReplicas.length ?  config.dbReplicas : [{
    host: db.host,
    port: db.port,
    username: db.user,
    password: db.password,
    database: db.database
}];

const replicationOptions: {query?: QueryOptions, replication?: ReplicationOptions} = {
    replication: {
        read: readReplicas,
        write: {
            host: db.host,
            port: db.port,
            username: db.user,
            password: db.password,
            database: db.database
        }
    }
};

const sequelize: Lazy<Sequelize> = lazy(() => {
    const result = new Sequelize(db.database, null, null, { ...dbOptions, ...replicationOptions });
    measures.measureProvider.instrument(result.connectionManager, "PgConnectionPool", ["getConnection"]);
    return result;
});

export default sequelize;
