import { lazy, Lazy, logging, redis } from "@skywind-group/sw-utils";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import { QueryTypes } from "sequelize";
import * as http from "http";
import { ValidationError } from "../errors";
import config from "../config";
import db from "../storage/db";

const log = logging.logger("sw-slot-engine:game-context-qa-api");
export const Redis: Lazy<redis.RedisPool<redis.RedisClient>> = lazy(() => redis.createRedisPool(config.redis));

interface BaseContextRequest {
    contextId: string;
}

interface UpdateExpireAtRequest extends BaseContextRequest {
    newExpireAt: string | number;
}

const OK_RESPONSE = {
    ok: true
};

const ERR_RESPONSE = {
    err: "Context not found"
};

const UPDATE_EXPIRE_SQL = "UPDATE game_contexts SET expire_at = :expireAt WHERE id = :contextId";

export default function(router: FastifyInstance, options, done) {
    router.post("/unload-context",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const request: BaseContextRequest = req.body as BaseContextRequest;
            validateBaseRequest(request);
            log.info(request, "Unload game context");
            await unloadContext(request.contextId);
            res.send(OK_RESPONSE);
        });

    router.post("/update-expire-at",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const expireAtRequest: UpdateExpireAtRequest = req.body as UpdateExpireAtRequest;
            validateExpireAt(expireAtRequest);
            const newExpireAt = new Date(expireAtRequest.newExpireAt);
            if (await updateExpireAt(expireAtRequest.contextId, newExpireAt)) {
                res.send(OK_RESPONSE);
            } else {
                res.send(ERR_RESPONSE);
            }
        });

    done();
}

async function unloadContext(contextId: string): Promise<void> {
    const client = await Redis.get().get();
    try {
        await client.zadd(config.namespaces.lastGameActivityKey, "0", contextId);
    } finally {
        await Redis.get().release(client);
    }
}

async function updateExpireAt(contextId: string, newExpireAt: Date): Promise<boolean> {
    const result = await db.get().transaction(async trx => {
        return await db.get().query(UPDATE_EXPIRE_SQL, {
            type: QueryTypes.UPDATE,
            transaction: trx,
            raw: true,
            replacements: {
                contextId,
                expireAt: newExpireAt
            },
            useMaster: true
        });
    });
    return result[1] === 1;
}

function validateBaseRequest(request: BaseContextRequest): void {
    if (!request || !request.contextId) {
        throw new ValidationError("contextId is empty");
    }
    validateContextId(request.contextId);
}

function validateContextId(contextId: string) {
    // games:context:53084:ccmmds_eu:sw_anca:web,  games:bns:4:playerCode_yk:sw_rireeu:web
    if (contextId.split(":").length !== 6) {
        throw new ValidationError("Invalid contextId");
    }
}

function validateExpireAt(expireAtRequest: UpdateExpireAtRequest): void {
    if (expireAtRequest.newExpireAt === undefined) {
        throw new ValidationError("Expire at should be specified");
    }
    if (expireAtRequest.newExpireAt !== null && isNaN(new Date(expireAtRequest.newExpireAt).getDate())) {
        throw new ValidationError("Invalid date: null, number or valid js-date string is allowed");
    }
}
