import { ClientResponse, GameEvent } from "@skywind-group/sw-game-core";
import { getUserAgent } from "../services/useragent";
import { SITE_COOKIE } from "../utils/common";
import { AsyncGameClient, AsyncGameConnection, AsyncRequest } from "../services/asyncconnection";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { SocketExtraHeaders } from "../services/socketExtraHeaders";
import { measures } from "@skywind-group/sw-utils";
import { logging } from "@skywind-group/sw-utils";
import { processIP } from "../utils/ip";
import { Socket } from "socket.io";

const log = logging.logger("sw-slot-engine:io");

export default function processSocketIOProtocol(socket: Socket) {
    log.info(`Socket connected: ${socket.id}`);
    const userAgent = getUserAgent(socket.request.headers["user-agent"]);
    const ip: string = processIP(
        socket.request.headers["x-forwarded-for"] as string || socket.request.connection.remoteAddress
    );
    const referrer: string = (socket.request as any).cookies ? (socket.request as any).cookies[SITE_COOKIE] : undefined;

    const client: AsyncGameClient = {
        sendResponse: (message: string, response: ClientResponse) => {
            socket.emit(message, response);
        },
        sendEvent: (event: GameEvent) => {
            socket.emit("event", event);
        },
        sendError: (err: SWError) => {
            socket.emit("game-error", {
                responseStatus: err.responseStatus,
                code: err.code,
                message: err.message,
                extraData: { ...err.extraData, traceId: measures.measureProvider.getTraceID() },
            });
        },
        disconnect: () => {
            log.info(`Socket received a disconnect message after Error`);
            socket.disconnect();
        }
    };
    const clientConnection = new AsyncGameConnection(client);

    socket.on("disconnect", (reason: any) => {
        log.info(`Socket received a disconnect message: ${reason}, SocketId: ${socket.id}`);
        let request: any = socket.request;
        if (!(socket.request as any)?.continueTransaction) {
            request = AsyncRequest.createRequest(socket.request as any);
        }
        return clientConnection.disconnect(request);
    });

    socket.on("init", AsyncRequest.startTransaction((request) => {
        request.userAgent = userAgent;
        request.ip = ip;
        request.referrer = referrer;
        SocketExtraHeaders.setUp(socket, request);

        return clientConnection.init("init", request);
    }));

    socket.on("reinit", AsyncRequest.startTransaction((request) => {
        request.userAgent = userAgent;
        request.ip = ip;
        request.referrer = referrer;
        SocketExtraHeaders.setUp(socket, request);

        return clientConnection.init("reinit", request);
    }));

    socket.on("replay", AsyncRequest.startTransaction((request) => {
        request.userAgent = userAgent;
        request.ip = ip;
        request.referrer = referrer;
        SocketExtraHeaders.setUp(socket, request);

        return clientConnection.init("replay", request);
    }));

    socket.on("play", AsyncRequest.startTransaction((request: any) => {
        request.userAgent = userAgent;
        request.ip = ip;
        return clientConnection.request("play", request);
    }));

    socket.on("transfer-in", AsyncRequest.startTransaction((request) => {
        request.request = "transfer-in";
        return clientConnection.request("transfer-in", request);
    }));

    socket.on("transfer-out", AsyncRequest.startTransaction((request) => {
        request.request = "transfer-out";
        return clientConnection.request("transfer-out", request);
    }));

    socket.on("jp-ticker", AsyncRequest.startTransaction((request) => {
        request.request = "jp-ticker";
        return clientConnection.request("jp-ticker", request);
    }));

    socket.on("balance", AsyncRequest.startTransaction((request) => {
        request.request = "balance";
        return clientConnection.request("balance", request);
    }));

    socket.on("choose-game", AsyncRequest.startTransaction((request) => {
        request.request = "choose-game";
        return clientConnection.request("choose-game", request);
    }));

    socket.on("state", AsyncRequest.startTransaction((request) => {
        request.request = "state";
        return clientConnection.request("state", request);
    }));

    socket.on("redeem-bns", AsyncRequest.startTransaction((request) => {
        request.request = "redeem-bns";
        return clientConnection.request("redeem-bns", request);
    }));

    socket.on("get-games", AsyncRequest.startTransaction((request) => {
        request.request = "get-games";
        return clientConnection.request("get-games", request);
    }));

    socket.on("player-action", AsyncRequest.startTransaction((request) => {
        request.request = "player-action";
        return clientConnection.request("player-action", request);
    }));

    socket.on("start-mini-game", AsyncRequest.startTransaction((request) => {
        request.request = "start-mini-game";
        return clientConnection.request("start-mini-game", request);
    }));

    socket.on("start-instant-jp-mini-game", AsyncRequest.startTransaction((request) => {
        request.request = "start-instant-jp-mini-game";
        return clientConnection.request("start-instant-jp-mini-game", request);
    }));

    socket.on("play-mini-game", AsyncRequest.startTransaction((request) => {
        request.request = "play-mini-game";
        return clientConnection.request("play-mini-game", request);
    }));

    socket.on("play-instant-jp-mini-game", AsyncRequest.startTransaction((request) => {
        request.request = "play-instant-jp-mini-game";
        return clientConnection.request("play-instant-jp-mini-game", request);
    }));

    socket.on("keep-alive", AsyncRequest.startTransaction((request) => {
        request.request = "keep-alive";
        return clientConnection.request("keep-alive", request);
    }));

    socket.on("change-nickname", AsyncRequest.startTransaction((request) => {
        request.request = "change-nickname";
        return clientConnection.request("change-nickname", request);
    }));
}
