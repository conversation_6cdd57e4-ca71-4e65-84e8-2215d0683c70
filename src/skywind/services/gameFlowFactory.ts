import { BaseRequest, GameFlowInfo, GameInitRequest, PushService, SomeGame } from "@skywind-group/sw-game-core";
import { GameFlowContext, MerchantLogoutResult, RequestContext, SpecialState } from "./context/gamecontext";
import { verifySessionToken } from "./tokens";
import * as Errors from "../errors";
import {
    GameContextBrokenIntegration,
    GameContextNotExists,
    GameFinalizedError,
    GameModuleError,
    NeedToRestartTheGame
} from "../errors";
import { JackpotContext } from "./jpn/jackpot";
import { createJackpotService } from "./jpn/jackpotService";
import { EngineGameFlow, GameConversionService } from "./gameflow";
import { Balance, createWalletManager } from "./wallet";
import { load, LoadedGameResult } from "./game/game";
import { GameData } from "./auth";
import { JackpotShortInfo } from "@skywind-group/sw-jpn-core";
import { GameSession } from "./gameSession";
import { GameProxy } from "./game/gameProxy";
import { GameContextID } from "./contextIds";
import { getGameFlowContextManager } from "./contextmanager/contextManagerImpl";
import { extractStartGameTokenData, GameReInitRequest } from "./requests";
import { injectRandomGenerator } from "./random";
import { logging } from "@skywind-group/sw-utils";
import { GameFlowContextImpl } from "./context/gameContextImpl";
import { ContextVariables } from "../utils/contextVariables";

export type GameLoader = (gameID: string) => Promise<LoadedGameResult>;
export const NO_GAME = async () => undefined;
export const DEFAULT_CONVERSION_SERVICE: GameConversionService = {
    toGameAmount: (gameInfo: GameFlowInfo, settings: any, amount: number): number => {
        return amount;
    },

    fromGameAmount: (gameInfo: GameFlowInfo, settings: any, amount: number): number => {
        return amount;
    }
};

const log = logging.logger("game-flow-factory");

export class GameFlowFactory {

    public static async createForRequest<T extends BaseRequest>(
        req: T,
        gameLoader: GameLoader = NO_GAME,
        checkConcurrentRequests = true,
        checkPending = true,
        dontCommitPending = false,
        isFinalizationUpdate = false
    ): Promise<EngineGameFlow<T>> {
        const sessionData = await verifySessionToken(req.gameSession);
        const session: GameSession = GameSession.create(req.gameSession, sessionData);
        const id = GameContextID.createFromString(sessionData.id);
        const contextManager = getGameFlowContextManager(session.gameMode);
        const context = await contextManager.findGameContextById(id);

        if (!context) {
            return Promise.reject(new GameContextNotExists());
        }

        this.checkSpecialState(context, isFinalizationUpdate);

        if (checkPending && context.pendingModification) {
            return Promise.reject(new Errors.NeedToRestartTheGame());
        }

        if (checkConcurrentRequests) {
            context.lastRequestId++;
        }
        if (req.gameSession !== context.session.id ||
            (checkConcurrentRequests && req.requestId !== context.lastRequestId)) {
            return Promise.reject(new Errors.ConcurrentAccessToGameSession());
        }

        const loadResult = await gameLoader(context.gameData.gameId);
        const game = loadResult ? loadResult.game : undefined;
        const result: EngineGameFlow<T> = new EngineGameFlow<T>(
            game, req, contextManager, context,
            GameFlowFactory.createConversionService(game),
            createWalletManager(context.gameData.gameTokenData, isFinalizationUpdate),
            createJackpotService(context));

        if (checkPending && !dontCommitPending) {
            await result.checkPending();
        }

        return result;
    }

    public static async createForInit(ctx: GameFlowContext,
                                      gameData: GameData,
                                      req: GameInitRequest & RequestContext,
                                      loadedGame: LoadedGameResult,
                                      pushService?: PushService,
                                      jpContext?: JackpotContext,
                                      jackpotInfo?: JackpotShortInfo[],
                                      isNewSession?: boolean): Promise<EngineGameFlow<GameInitRequest>> {
        const gameTokenData = gameData.gameTokenData;
        if (!gameData.limits) {
            return Promise.reject(new Errors.LimitsNotFound());
        }
        const id: GameContextID = GameContextID.create(gameTokenData.gameCode,
            gameTokenData.brandId,
            gameTokenData.playerCode,
            req.deviceId,
            gameData.gameTokenData.playmode);
        const session = await GameSession.generate(id, gameData.gameTokenData.playmode);
        const contextManager = getGameFlowContextManager(gameData.gameTokenData.playmode);
        const requestCtx: RequestContext = GameFlowFactory.createRequestContext(req, gameData);

        const shouldUseOldGameTokenData = gameData.settings?.skipPendingPaymentReAuthentication
            && ctx?.pendingModification?.walletOperation
            && !ctx?.pendingModification?.walletOperation?.gameToken
            && ctx?.gameData?.gameTokenData?.token;
        if (shouldUseOldGameTokenData) {
            ctx.pendingModification.walletOperation.gameToken = ctx.gameData.gameTokenData.token;
        }

        const context = await contextManager.createOrUpdate(
            id, ctx, session, gameData, loadedGame?.moduleName, requestCtx, jackpotInfo, jpContext, isNewSession);

        await this.flushSpecialStateIfPossible(context);
        this.checkSpecialState(context);
        await this.flushLogoutResult(context);

        const result: EngineGameFlow<GameInitRequest> = new EngineGameFlow<GameInitRequest>(
            loadedGame?.game,
            req,
            contextManager,
            context,
            GameFlowFactory.createConversionService(loadedGame?.game),
            createWalletManager(gameTokenData),
            createJackpotService(context),
            pushService,
            gameData.balance);
        try {
            ContextVariables.setUpRound(ctx?.roundId);
            await result.checkPending(gameData.balance);
        } finally {
            ContextVariables.cleanupRound();
        }
        return result;
    }

    public static async createForReInit(req: GameReInitRequest & RequestContext,
                                        loadedGame: LoadedGameResult,
                                        pushService?: PushService): Promise<EngineGameFlow<GameInitRequest>> {
        const gameSessionData = await verifySessionToken(req.gameSession);
        const startGameTokenData = await extractStartGameTokenData(req);
        const contextId: GameContextID = GameContextID.createFromString(gameSessionData.id);
        const contextManager = getGameFlowContextManager(startGameTokenData.playmode);
        const context = await contextManager.findGameContextById(contextId);
        if (!context) {
            return Promise.reject(new Errors.GameSessionClosed());
        }

        this.checkSpecialState(context);

        const gameData = context.gameData;
        if (startGameTokenData.playmode && startGameTokenData.playmode !== context.gameData.gameTokenData.playmode) {
            return Promise.reject(new Errors.GameReInitError("wrong playmode"));
        } else if (context.session.sessionId !== gameSessionData.sessionId) {
            return Promise.reject(new Errors.GameReInitError("wrong sessions id"));
        } else if (context.gameData.gameId !== req.gameId) {
            return Promise.reject(new Errors.GameReInitError("wrong game id"));
        } else if (contextId.deviceId !== req.deviceId) {
            return Promise.reject(new Errors.GameReInitError("wrong device id"));
        } else if (contextId.brandId !== startGameTokenData.brandId ||
            contextId.playerCode !== startGameTokenData.playerCode ||
            contextId.gameCode !== startGameTokenData.gameCode) {
            return Promise.reject(new Errors.GameReInitError("wrong start token"));
        }
        const result: EngineGameFlow<GameInitRequest> = new EngineGameFlow<GameInitRequest>(
            loadedGame?.game,
            {
                ...req,
                request: "init",
                gameId: gameData.gameId,
                deviceId: contextId.deviceId,
            } as any,
            contextManager,
            context,
            GameFlowFactory.createConversionService(loadedGame?.game),
            createWalletManager(context.gameData.gameTokenData),
            createJackpotService(context),
            pushService);

        await result.checkPending();

        return result;
    }

    public static async createFromContext<T extends BaseRequest>(context: GameFlowContext,
                                                                 req: T,
                                                                 game?: SomeGame,
                                                                 pushService?: PushService,
                                                                 checkConcurrentRequests = true,
                                                                 checkPending = true,
                                                                 offlineMode = false,
                                                                 defaultBalance?: Balance): Promise<EngineGameFlow<T>> {
        if (!context) {
            return Promise.reject(new GameContextNotExists());
        }

        if (context.corrupted) {
            return Promise.reject(new Errors.GameContextStateIsBroken());
        }
        const gameData = context.gameData;
        const contextManager = getGameFlowContextManager(gameData.gameTokenData.playmode);

        const result: EngineGameFlow<T> = new EngineGameFlow<T>(
            game && GameProxy.getProxy(game),
            req,
            contextManager,
            context,
            GameFlowFactory.createConversionService(game),
            createWalletManager(gameData.gameTokenData, offlineMode),
            createJackpotService(context),
            pushService, defaultBalance);

        if (checkPending) {
            this.checkSpecialState(context);
            await result.checkPending();
        }

        if (checkConcurrentRequests) {
            await result.incrementAndCheckRequestId(req);
        }

        return result;
    }

    public static async createFromContextWithRefresh(context: GameFlowContext,
                                                     req: any,
                                                     game?: SomeGame,
                                                     pushService?: PushService,
                                                     checkConcurrentRequests = true,
                                                     checkPending = true,
                                                     offlineMode = false,
                                                     defaultBalance?: Balance): Promise<EngineGameFlow> {
        if (!context) {
            return Promise.reject(new GameContextNotExists());
        }

        if (context.corrupted) {
            return Promise.reject(new Errors.GameContextStateIsBroken());
        }
        const contextManager = getGameFlowContextManager(context.playMode);
        const freshContext = await contextManager.findGameContextById(context.id);
        if (freshContext?.version !== (context as GameFlowContextImpl).version) {
            log.info("Context was modified before player disconnected");
        }
        return GameFlowFactory.createFromContext(freshContext,
            req,
            game,
            pushService,
            checkConcurrentRequests,
            checkPending,
            offlineMode,
            defaultBalance);
    }

    public static async createForGameContextFinalization(context: GameFlowContext,
                                                         skipGameLoadError?: boolean): Promise<EngineGameFlow> {
        const defaultBalance: Balance = { main: 0, currency: context?.gameData?.gameTokenData?.currency };
        return this.createForRecovery(context, defaultBalance, skipGameLoadError);
    }

    public static async createForRecovery(
        context: GameFlowContext,
        defaultBalance?: Balance,
        skipGameLoadError?: boolean
    ): Promise<EngineGameFlow> {
        const gameId = context?.gameData?.gameId;
        const ignoreError = skipGameLoadError || (gameId && !gameId.startsWith("sw_"));
        const loadResult = await load(gameId, ignoreError);
        await this.flushSpecialStateIfPossible(context);
        const result = await GameFlowFactory.createFromContext(context,
            undefined,
            loadResult?.game,
            undefined,
            false,
            false,
            true, defaultBalance);
        await injectRandomGenerator(result);
        return result;
    }

    public static async createOfflineFlow(context: GameFlowContext, gameLoadEnabled = false): Promise<EngineGameFlow> {
        const game = gameLoadEnabled ? (await load(context?.gameData?.gameId))?.game : undefined;
        return GameFlowFactory.createFromContext(context, undefined, game, undefined, false, false, true);
    }

    private static createConversionService(game: SomeGame): GameConversionService {
        const anyGame = game as any;
        if (anyGame && anyGame.toGameAmount && anyGame.fromGameAmount) {
            return anyGame as GameConversionService;
        } else {
            return DEFAULT_CONVERSION_SERVICE;
        }
    }

    private static createRequestContext(req: RequestContext, gamedata: GameData): RequestContext {
        return {
            userAgent: req.userAgent,
            ip: req.ip,
            screenSize: req.screenSize,
            language: gamedata.player ? gamedata.player.language : undefined,
            country: gamedata.player ? gamedata.player.country : undefined,
            playedFromCountry: gamedata.playedFromCountry,
            operatorCountry: gamedata.operatorCountry,
            operatorPlayerCountry: gamedata.operatorPlayerCountry,
            deviceData: req.deviceData
        };
    }

    private static checkSpecialState(context: GameFlowContext, isFinalizationUpdate?: boolean) {
        if (context.specialState === SpecialState.BROKEN_INTEGRATION) {
            throw new GameContextBrokenIntegration();
        } else if (context.specialState === SpecialState.FINALIZING && !isFinalizationUpdate) {
            throw new GameFinalizedError();
        } else if (context.specialState === SpecialState.CANNOT_COMPLETE_PAYMENT) {
            throw new NeedToRestartTheGame();
        } else if (context.specialState === SpecialState.BROKEN_GAME_CONTEXT) {
            throw new GameModuleError();
        }
    }

    private static async flushSpecialStateIfPossible(context: GameFlowContext) {
        if (context.specialState === SpecialState.CANNOT_COMPLETE_PAYMENT
            || context.specialState === SpecialState.BROKEN_GAME_CONTEXT) {
            await context.clearSpecialState();
        }
    }

    private static async flushLogoutResult(context: GameFlowContext) {
        if (context.logoutResult === MerchantLogoutResult.SKIP_LOGIN) {
            await context.setLogoutResult(null);
        }
    }
}
