import { GameFlowContext, JackpotPendingModification, RoundStatistics, SpecialState } from "./context/gamecontext";
import { GameContextID } from "./contextIds";
import { logging, measures, publicId } from "@skywind-group/sw-utils";
import * as Errors from "../errors";
import {
    CannotFinalizePaymentIsIncompleteError,
    ForbiddenToForceFinishContextError,
    ForbidToRecoverGameWithJPError,
    ForceFlagIsRequiredForOperationError,
    GameContextNotExists,
    GameFinalizedError, GameNotFoundError,
    isCannotCompletePaymentError,
    RevertForBrokenPaymentError,
    RoundNotFoundError,
    UnsupportedPlaymodeForRevert
} from "../errors";
import { createWalletManager, PaymentOperation, SplitPaymentOperation, WalletOperation } from "./wallet";
import { ContextManager } from "./contextmanager/contextManager";
import { GameFlowInfoImpl } from "./context/gameFlowInfo";
import CompletionService from "./pendingCompletionService";
import { ExtendedGameHistory } from "../history/history";
import { WalletOperationFactory } from "./walletOperationFactory";
import { GameFlowFactory } from "./gameFlowFactory";
import { GameTokenData } from "./tokens";
import { ContextUpdate, GameContext, GameContextPersistencePolicy, GameMode } from "@skywind-group/sw-game-core";
import { EngineGameFlow, PreparedPendingUpdate } from "./gameflow";
import { PlayMode } from "./playMode";
import { getGameFlowContextManager } from "./contextmanager/contextManagerImpl";
import { AutoPlayer, AutoPlayResult } from "@skywind-group/sw-game-autoplayer";
import { createAutoPlayer } from "./gameautoplayerfactory";
import { ContextVariables } from "../utils/contextVariables";
import { PaymentDelegate } from "./paymentDelegate";
import { RecoveryType } from "./offlinestorage/offlineCommands";
import { getSmResult } from "./context/gameContextImpl";
import { ITGFinalizationRequest, ITGHttpGateway } from "./itgHttpGateway";
import measure = measures.measure;
import { GameFinalizationType } from "@skywind-group/sw-wallet-adapter-core";
import { JackpotUtil } from "./jpn/jackpotUtil";

const log = logging.logger("sw-slot-engine:gamerecovery");

export type ForceFinishResult = "force-finished";

export interface ForceFinishResponse {
    result: ForceFinishResult;
    roundStatistics?: RoundStatistics;
}

export interface RecoveryRequestData {
    gameContextId?: string;
    round: string | RoundToRecover;
    force?: boolean;
    reverted?: boolean;
    closeInSWWalletOnly?: boolean;
}

export enum FinalizeBrokenPaymentType {
    RETRY = "retry",
    DISABLE = "disable",
    MARK_FINALIZED = "markFinalized"
}

export interface FinalizeRequestData {
    brandId?: number;
    merchantSessionId?: string;
    gameContextId?: string;
    operatorSupportsFinalization?: boolean;
    /**
     * Finalization type. Default is MARK_FINALIZED
     */
    finalizeBrokenPayment?: FinalizeBrokenPaymentType;
    round?: string | RoundToRecover;
    gameFinalizationType?: GameFinalizationType;
    brandFinalizationType?: string;
    // mark game context with special state FINALIZING to prevent concurrency between finalization process and player
    lockContext?: true;
    closeInSWWalletOnly?: boolean;
}

export interface ExpireGameRequestData extends FinalizeRequestData {
    gameCode: string;
    roundId: string;
    playerCode: string;
}

export interface StartFinalizeResponse {
    gameContextId: string;
    currency: string;
    roundId: string;
    roundPID: string;
    gameTokenData: GameTokenData;
    roundStatistics?: Pick<RoundStatistics, Exclude<keyof RoundStatistics, "smResults">> & { "smResult"?: string };
}

export type RevertResult = "reverted";

export interface RevertResponse {
    result: RevertResult;
}

export type RetryResult = "finished";

export interface RetryResponse {
    result: RetryResult;
}

/**
 * Interface of RoundHistory received from MAPI to recover
 */
export interface RoundToRecover {
    roundId: string;
    gameId?: string;
    brandId: number;
    playerCode: string;
    device?: string;
    gameCode: string;
    currency: string;
    isTest: boolean;
    bet: number;
    win: number;
    revenue?: number;
    totalEvents?: number;
    balanceBefore?: number;
    balanceAfter?: number;
    broken?: boolean;
    firstTs?: Date;
    ts?: Date;
    finished?: boolean;
    internalRoundId?: string;
}

export class GameRecoveryService {
    private readonly itgHttpGateway: ITGHttpGateway;

    constructor(private readonly contextManager: ContextManager) {
        this.itgHttpGateway = new ITGHttpGateway();
    }

    @measure({ name: "GameRecoveryService.forceFinish", isAsync: true })
    public async forceFinish(request: RecoveryRequestData): Promise<ForceFinishResponse> {
        const id = await this.getGameContextId(request);
        const roundId = this.getRoundId(request);
        if (!id) {
            log.info("Could not find an unfinished game context for round %s", roundId);
            return Promise.reject(new RoundNotFoundError(roundId));
        }
        ContextVariables.setUp(id, roundId);
        const context: GameFlowContext = await this.loadGameContext(id);
        log.info(context, "Force-finish operation started");
        // tslint:disable-next-line:triple-equals
        if (context && context.roundId == roundId && context.broken) {
            if (context.specialState === SpecialState.FINALIZING && !request.force) {
                return Promise.reject(new GameFinalizedError());
            }

            if (CompletionService.requireRetry(context) && !request.force) {
                return Promise.reject(new ForceFlagIsRequiredForOperationError("force-finish with pending payment"));
            }

            const failedOnFirstBet = context?.pendingModification?.walletOperation?.operation === "split-payment" &&
                (context.pendingModification.walletOperation as SplitPaymentOperation).balanceAfterDebit === undefined;

            if (context.jackpotPending && !failedOnFirstBet) {
                return Promise.reject(new ForbidToRecoverGameWithJPError());
            }

            this.checkPersistencePolicy(context, request.force, request.closeInSWWalletOnly);

            const recoveryType = request.reverted ? RecoveryType.REVERT : RecoveryType.FORCE_FINISH;

            // for finalization and forcefinish we store game context in special Postgres table for history
            await this.contextManager.archiveGameContext(context, recoveryType);

            if (request.reverted) {
                await context.forceCloseRound(recoveryType);

                return {
                    result: "force-finished"
                };
            }

            let flow: EngineGameFlow;
            try {
                flow = await GameFlowFactory.createForGameContextFinalization(context);
            } catch (err) {
                if (err instanceof GameNotFoundError) {
                    log.warn(err, "Game not found. Skip loading the game module.");
                    flow = await GameFlowFactory.createForGameContextFinalization(context, true);
                } else {
                    log.error(err, "Could not create flow for force-finish.");
                    return Promise.reject(err);
                }
            }
            const finalizeOperation = await flow.walletOperationFactory.createFinalizeGameOperation(
                { brandFinalizationType: "forceFinish", closeInSWWalletOnly: request.closeInSWWalletOnly },
                RecoveryType.FORCE_FINISH
            );

            // clear previous pending and remove pending contribution
            await context.rollbackPendingModification(true);
            // writes round statistics data to game context and creates 'pending' payment in it
            await context.prepareFinalizeGame(finalizeOperation);

            const roundStatistics = context.round;

            // sends round statistics data to MAPI with finalize-game operation
            await CompletionService.complete(context, flow);

            await context.forceCloseRound(recoveryType);

            return {
                result: "force-finished",
                roundStatistics
            };
        } else {
            log.info("Could not find round that can be force-closed for context %s and round %s", id.asString(),
                roundId);
            return Promise.reject(new RoundNotFoundError(roundId));
        }
    }

    @measure({ name: "GameRecoveryService.revert", isAsync: true })
    public async revert(request: RecoveryRequestData): Promise<RevertResponse> {
        const id = await this.getGameContextId(request);
        const roundId = this.getRoundId(request);
        ContextVariables.setUp(id, roundId);
        const context: GameFlowContext = await this.loadGameContext(id);
        log.info(context, "Revert operation started");

        this.validateContextAndRoundId(context, roundId, request.force, request.closeInSWWalletOnly);

        if (context?.specialState === SpecialState.FINALIZING) {
            return Promise.reject(new GameFinalizedError());
        }

        await this.contextManager.archiveGameContext(context, RecoveryType.REVERT);

        const walletManager = createWalletManager(context.gameData.gameTokenData);
        const transactionId = await walletManager.generateTransactionId();

        const history: ExtendedGameHistory = {
            type: "revert-game",
            roundEnded: true,
            data: {},
            ts: new Date()
        };

        const rollbackOperation: PaymentOperation = {
            operation: "payment",
            transactionId,
            deviceId: context.id.deviceId,
            currency: context.gameData.gameTokenData.currency,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: true,
            win: -context.round.totalWin,
            bet: -context.round.totalBet,
            ts: history.ts,
            actions: [
                {
                    action: "credit",
                    attribute: "balance",
                    amount: context.round.totalBet,
                    changeType: "win",

                },
                {
                    action: "debit",
                    attribute: "balance",
                    amount: context.round.totalWin,
                    changeType: "bet",
                }
            ]
        };

        await context.updatePendingModification(
            rollbackOperation,
            undefined,
            undefined,
            history, undefined,
            undefined);

        const balance = await walletManager.commitOperation(rollbackOperation);
        await context.commitPendingModification(
            balance,
            undefined,
            undefined,
            RecoveryType.REVERT);
        return { result: "reverted" };
    }

    public validateContextAndRoundId(
        context: GameFlowContext,
        roundId: string,
        force: boolean,
        closeInSWWalletOnly: boolean
    ) {
        const playMode = context?.gameData?.gameTokenData?.playmode;

        if (!PlayMode.supportsRevert(playMode)) {
            throw new UnsupportedPlaymodeForRevert(playMode);
        }

        // tslint:disable-next-line:triple-equals
        if (!(context && context.roundId == roundId && context.broken)) {
            throw new RoundNotFoundError(roundId);
        }

        if ((context.pendingModification || context.jackpotPending) && !force) {
            throw new ForceFlagIsRequiredForOperationError("revert with pending payment");
        }

        if (context.jackpotPending) {
            throw new ForbidToRecoverGameWithJPError();
        }

        if (!context.round) {
            throw new RevertForBrokenPaymentError();
        }

        this.checkPersistencePolicy(context, force, closeInSWWalletOnly);
    }

    @measure({ name: "GameRecoveryService.retryPending", isAsync: true })
    public async retryPending(request: RecoveryRequestData): Promise<RetryResponse> {
        const id = await this.getGameContextId(request);
        const roundId = this.getRoundId(request);
        ContextVariables.setUp(id, roundId);
        const context: GameFlowContext = await this.loadGameContext(id);

        await this.checkState(context);
        // tslint:disable-next-line:triple-equals
        if (context && context.roundId == roundId && CompletionService.requireRetry(context)) {
            await CompletionService.retry(context);

            return { result: "finished" };
        } else {
            log.info("Skip retry. Context %s has no pending operation for round %s", id.asString(),
                roundId);
            return Promise.reject(new RoundNotFoundError(roundId));
        }
    }

    @measure({ name: "GameRecoveryService.transferOut", isAsync: true })
    public async transferOut(request: RecoveryRequestData): Promise<RetryResponse> {
        const id = await this.getGameContextId(request);
        const roundId = this.getRoundId(request);
        ContextVariables.setUp(id, roundId);
        const context: GameFlowContext = await this.loadGameContext(id);
        // tslint:disable-next-line:triple-equals
        if (context && context.roundId == roundId) {
            await this.checkState(context);

            if (CompletionService.requireRetry(context)) {
                await CompletionService.retry(context);
            }

            if (context.requireTransferOut) {
                const flow = await GameFlowFactory.createOfflineFlow(context);
                await flow.transferAllOut();
            }
            return { result: "finished" };
        } else {
            return Promise.reject(new RoundNotFoundError(roundId));
        }
    }

    private async checkState(context: GameFlowContext): Promise<void> {
        if (context && context.specialState === SpecialState.BROKEN_INTEGRATION) {
            await context.clearSpecialState();
        } else if (context && context.specialState === SpecialState.FINALIZING) {
            return Promise.reject(new GameFinalizedError());
        }
    }

    @measure({ name: "GameRecoveryService.finalize", isAsync: true })
    public async finalize(request: FinalizeRequestData): Promise<RoundStatistics> {
        const ctx = await this.doStartFinalization(request, request.lockContext);
        const flow = await GameFlowFactory.createForGameContextFinalization(ctx);
        if (ctx.unfinished) {
            let update: ContextUpdate<GameContext>;
            ContextVariables.setUpWithContext(ctx);
            if (request.gameFinalizationType === GameFinalizationType.AUTO_PLAY) {
                update = await this.autoPlay(flow, ctx, request);
            } else {
                update = await this.finalizeUsingFinalizationFromGame(flow, ctx, request);
            }
            const finalizeOperation = await flow.walletOperationFactory.createFinalizeGameOperation(
                request,
                RecoveryType.FINALIZE);
            // writes round statistics data to game context
            await ctx.prepareFinalizeGame(finalizeOperation, update?.context);

            /*
            On last spin in round we are locking the context (see comments in method applyFinalizationUpdate).
            Payment already committed - we can unlock the context, because finalize game operation is already
            stored in pending.
            */
            if (!request.lockContext) {
                await ctx.clearSpecialState();
            }
        }

        const roundStatistics = ctx.round;

        // sends round statistics data to MAPI with finalize-game operation
        await CompletionService.complete(ctx, flow);

        // If the "finalizing" special state is still set, clear it, as finalization is completed
        if (ctx.specialState === SpecialState.FINALIZING) {
            await ctx.clearSpecialState();
        }

        return roundStatistics;
    }

    @measure({ name: "GameRecoveryService.finalizeItgGame", isAsync: true })
    public async finalizeItgGame(request: FinalizeRequestData): Promise<RoundStatistics> {
        let ctx = await this.loadContextForFinalize(request);
        const hasBrokenPayment = ctx?.brokenPayment;
        const shouldSendFinalizeGameOperation =
            (hasBrokenPayment && request.finalizeBrokenPayment === FinalizeBrokenPaymentType.MARK_FINALIZED) ||
            (!hasBrokenPayment && ctx?.unfinished);
        ctx = await this.doStartFinalization(request, request.lockContext);
        let flow = await GameFlowFactory.createForGameContextFinalization(ctx);
        const isGameStateEmpty = !ctx.gameContext || !Object.keys(ctx.gameContext).length;

        if (ctx.unfinished && !isGameStateEmpty) {

            ContextVariables.setUpWithContext(ctx);

            if (JackpotUtil.isInstantJackpotMiniGame(ctx)) {
                await this.autoPlay(flow, ctx, request, true);
            } else {
                const { gameData } = flow.flowContext;
                // Throws error in case of ITG "finalize" call failure and the process stops
                await this.itgHttpGateway.triggerItgFinalization({
                    gameSession: flow.info().gameSessionId,
                    gameState: ctx.gameContext,
                    gameId: ctx.gameData.gameId,
                    operatorSupportsFinalization: request.operatorSupportsFinalization,
                    brandFinalizationType: request.brandFinalizationType,
                    playerCode: gameData?.gameTokenData?.playerCode,
                    limits: gameData?.limits,
                    settings: gameData?.settings,
                    gameSettings: gameData?.gameSettings,
                    jrsdSettings: gameData?.jrsdSettings,
                    brandSettings: gameData?.brandSettings,
                    lastRequestId: flow.flowContext.lastRequestId,
                    balance: await flow.getBalance(true)
                } as ITGFinalizationRequest);

                // re-create the flow with the latest context updates
                ctx = await this.loadContextForFinalize(request);
                flow = await GameFlowFactory.createForGameContextFinalization(ctx);

                if (JackpotUtil.isInstantJackpotMiniGame(ctx)) {
                    await this.autoPlay(flow, ctx, request, true);
                }
            }

            /*
            On last spin in round we are locking the context (see comments in method applyFinalizationUpdate).
            Payment already committed - we can unlock the context, because finalize game operation is already
            stored in pending.
            */
            ctx = await this.loadContextForFinalize(request);
            if (!request.lockContext) {
                await ctx.clearSpecialState();
            }
        }
        // re-create the flow with the latest context updates
        ctx = await this.loadContextForFinalize(request);
        flow = await GameFlowFactory.createForGameContextFinalization(ctx);
        const roundStatistics = { ...ctx.round };
        if (shouldSendFinalizeGameOperation) {
            const finalizeOperation = await flow.walletOperationFactory.createFinalizeGameOperation(
                { brandFinalizationType: request.brandFinalizationType },
                RecoveryType.FINALIZE);
            // Writes round statistics data to the game context
            await flow.flowContext.prepareFinalizeGame(finalizeOperation);
        }

        await CompletionService.complete(ctx, flow);

        // If the "finalizing" special state is still set, clear it, as finalization is completed
        if (ctx.specialState === SpecialState.FINALIZING) {
            await ctx.clearSpecialState();
        }

        return roundStatistics;
    }

    private async finalizeUsingFinalizationFromGame(flow: EngineGameFlow,
                                                    ctx: GameFlowContext,
                                                    request: FinalizeRequestData): Promise<ContextUpdate<GameContext>> {
        const update: ContextUpdate<GameContext> = await this.getFinalizeContextUpdate(flow);
        if (this.operatorSupportsFinalizationPayments(request)) {
            await this.checkGuaranteedWinnings(request, flow, ctx, update);
        }
        return update;
    }

    private async checkGuaranteedWinnings(request: FinalizeRequestData,
                                          flow: EngineGameFlow,
                                          ctx: GameFlowContext,
                                          update: ContextUpdate<GameContext>) {
        let pendingUpdate: PreparedPendingUpdate;
        if (this.hasGuaranteedWinning(update)) {
            pendingUpdate = await flow.preparePending(update);
        } else {
            // Even if there are no guaranteed winnings, send zero win anyway to notify the operator
            // that the round is closed
            pendingUpdate = await flow.preparePending(this.createZeroWinPaymentUpdate(update));
        }
        await GameRecoveryService.applyFinalizationUpdate(flow, ctx, pendingUpdate, request.brandFinalizationType);
    }

    private createZeroWinPaymentUpdate(update: ContextUpdate<GameContext>): ContextUpdate<GameContext> {
        if (!update) {
            update = {};
        }
        if (!update.payment) {
            update.payment = {
                win: 0
            };
        }
        // Ensure that roundEnded is set to true
        if (!update.history) {
            update.history = {
                type: "finalize",
                roundEnded: true,
                data: {}
            };
        } else {
            update.history.roundEnded = true;
        }
        return update;
    }

    private hasGuaranteedWinning(update: ContextUpdate<GameContext>): boolean {
        return update && !!(update.payment || update.jackpotAction);
    }

    // Returns true if operator has entity.settings.finalizationSupport: 'offlinePayments' or 'roundStatistics' set.
    // Keep in mind, that there are cases when operator has 'roundStatistics' option set which means that
    // he wants to receive statistics only to close round but auto-play transactions or presence of 'guaranteed payment'
    // will cause an extra offline payment(s) to be sent to MAPI with request.finalizationType = "roundStatistics"
    // before the final 'roundStatistics' payment will be sent (and there, in MerchantPlayService it will log these
    // non-final payments in OUR wallet for the sake of having it in BI, aggreg reports and game history and WILL NOT
    // send it to Operator's wallet - see MerchantPlayService.doPaymentOperation()).
    private operatorSupportsFinalizationPayments(request: FinalizeRequestData): boolean {
        return request.operatorSupportsFinalization;
    }

    /**
     * Prepares payment that is a result of a finalization process that will be sent to MAPI and probably further to
     * operator. For example, each auto-play-generated transaction or game's guaranteed payment
     * (such like cashpot in multi-bet game) will use this method to send payment to MAPI.
     */
    public static async applyFinalizationUpdate(flow: EngineGameFlow,
                                                ctx: GameFlowContext,
                                                pendingUpdate: PreparedPendingUpdate,
                                                finalizationType: string) {

        if (pendingUpdate.hasPending) {
            await this.validateFinalizationPaymentFromGame(pendingUpdate, ctx);
            this.preparePaymentForFinalization(FinalizeBrokenPaymentType.MARK_FINALIZED,
                pendingUpdate.walletOperation,
                pendingUpdate.jackpotPending,
                finalizationType);

            ContextVariables.setUpWithContext(ctx);

            const contextShouldBeLocked = pendingUpdate?.history?.roundEnded === true;
            const isInstantJpTriggered = flow.jackpotFlow.isInstantJpMiniGameTriggered();
            const isInstantJpFinished = JackpotUtil.isInstantJackpotMiniGameFinished(pendingUpdate?.history);

            if (contextShouldBeLocked && (!isInstantJpTriggered || isInstantJpFinished)) {
                /*
                The last one history item will be finalization event. To provide atomicity of two payments
                (last payment from game and finalize-game) and consistency of game state we need to lock the context.
                Since context locked offline retries disabled and player cannot start game concurrently with
                finalization logic
                */
                await ctx.setSpecialState(SpecialState.FINALIZING);
                pendingUpdate.history.roundEnded = false;

            }
            await flow.flowContext.updatePendingModification(pendingUpdate.walletOperation,
                pendingUpdate.context,
                undefined,
                pendingUpdate.history,
                pendingUpdate.jackpotPending,
                pendingUpdate.analytics);
            try {
                await CompletionService.completePayment(flow.flowContext, flow);
            } catch (e) {
                if (isCannotCompletePaymentError(e) && contextShouldBeLocked) {
                    if (flow?.flowContext?.pendingModification?.history) {
                        /* Restore roundEnded flag because context is in cannot complete payment state to prevent case
                        when player will start game and continue already finished round */
                        flow.flowContext.pendingModification.history.roundEnded = true;
                        await flow.flowContext.updatePending();
                    }
                }
                throw e;
            }
        }
    }

    @measure({ name: "GameRecoveryService.startFinalize", isAsync: true })
    public async startFinalize(request: FinalizeRequestData): Promise<StartFinalizeResponse> {
        const ctx = await this.doStartFinalization(request, true);

        if (ctx.unfinished) {
            const flow = await GameFlowFactory.createForGameContextFinalization(ctx);
            let update: ContextUpdate<GameContext>;
            if (request.gameFinalizationType === GameFinalizationType.AUTO_PLAY) {
                update = await this.autoPlay(flow, ctx, request);
            } else {
                update = await this.finalizeUsingFinalizationFromGame(flow, ctx, request);
            }
            await flow.flowContext.update(update?.context);
        }

        const isUnderRefund = this.isUnderRefund(ctx);
        const hasRoundDetails = !!ctx.round || isUnderRefund;
        return {
            gameContextId: ctx.id.asString(),
            currency: ctx.currencyForHistory,
            roundId: hasRoundDetails ? ctx.roundId : undefined,
            roundPID: hasRoundDetails ? publicId.instance.encode(ctx.roundId) : undefined,
            gameTokenData: hasRoundDetails ? ctx.gameData.gameTokenData : undefined,
            roundStatistics: this.getRoundStatistics(ctx, isUnderRefund),
        };
    }

    @measure({ name: "GameRecoveryService.startItgFinalize", isAsync: true })
    public async startItgFinalize(request: FinalizeRequestData): Promise<StartFinalizeResponse> {
        let ctx = await this.doStartFinalization(request, true);
        const isUnderRefund = this.isUnderRefund(ctx);
        const hasRoundDetails = !!ctx.round || !!ctx.lastRound || isUnderRefund;
        const flow = await GameFlowFactory.createForGameContextFinalization(ctx);
        const isGameStateEmpty = ctx.gameContext && !Object.keys(ctx.gameContext).length;
        if (ctx.unfinished && !isGameStateEmpty) {
            ContextVariables.setUpWithContext(ctx);
            const { gameData } = flow.flowContext;
            // Throws error in case of ITG "finalize" call failure and the process stops
            await this.itgHttpGateway.triggerItgFinalization({
                gameSession: flow.info().gameSessionId,
                gameState: ctx.gameContext,
                gameId: ctx.gameData.gameId,
                operatorSupportsFinalization: request.operatorSupportsFinalization,
                brandFinalizationType: request.brandFinalizationType,
                playerCode: gameData?.gameTokenData?.playerCode,
                limits: gameData?.limits,
                settings: gameData?.settings,
                gameSettings: gameData?.gameSettings,
                jrsdSettings: gameData?.jrsdSettings,
                brandSettings: gameData?.brandSettings,
                lastRequestId: flow.flowContext.lastRequestId,
                balance: await flow.getBalance(true)
            } as ITGFinalizationRequest);
        }
        ctx = await this.loadContextForFinalize(request);
        const roundStatistics = this.getRoundStatisticsForItg(ctx, isUnderRefund);
        log.info(ctx.round || ctx.lastRound, "ITG Finalization - Round Statistics");
        return {
            gameContextId: ctx.id.asString(),
            currency: ctx.currencyForHistory,
            roundId: hasRoundDetails ? ctx.roundId : undefined,
            roundPID: hasRoundDetails ? publicId.instance.encode(ctx.roundId) : undefined,
            gameTokenData: hasRoundDetails ? ctx.gameData.gameTokenData : undefined,
            roundStatistics
        };
    }

    private getRoundStatistics(ctx: GameFlowContext, isUnderRefund: boolean) {
        if (isUnderRefund) {
            const result = {
                ...ctx.round || {
                    totalBet: 0,
                    totalWin: 0,
                    totalEvents: 1,
                    balanceBefore: undefined,
                    balanceAfter: undefined,
                    totalJpContribution: 0,
                    totalJpWin: 0,
                }
            };

            result.totalBet = (ctx?.round?.totalBet || 0) + ctx?.pendingModification?.walletOperation?.bet || 0;
            result.refund = ctx?.pendingModification?.walletOperation?.bet;

            return result;
        } else {
            if (ctx.round && ctx.round.smResults) {
                const { smResults, ...rest } = ctx.round;
                return { smResult: getSmResult(smResults), ...rest };
            }
            return ctx.round;
        }
    }

    private getRoundStatisticsForItg(ctx: GameFlowContext, isUnderRefund: boolean) {
        if (isUnderRefund) {
            const result = {
                ...ctx.round || ctx.lastRound || {
                    totalBet: 0,
                    totalWin: 0,
                    totalEvents: 1,
                    balanceBefore: undefined,
                    balanceAfter: undefined,
                    totalJpContribution: 0,
                    totalJpWin: 0,
                }
            };

            const totalBet = ctx?.round?.totalBet || ctx?.lastRound?.totalBet || 0;
            result.totalBet = totalBet + ctx?.pendingModification?.walletOperation?.bet || 0;
            result.refund = ctx?.pendingModification?.walletOperation?.bet;

            return result;
        } else {
            if (ctx.round && ctx.round.smResults) {
                const { smResults, ...rest } = ctx.round;
                return { smResult: getSmResult(smResults), ...rest };
            } else if (ctx.lastRound && ctx.lastRound.smResults) {
                const { smResults, ...rest } = ctx.lastRound;
                return { smResult: getSmResult(smResults), ...rest };
            }
            return ctx.round || ctx.lastRound;
        }
    }

    @measure({ name: "GameRecoveryService.completeFinalize", isAsync: true })
    public async completeFinalize(request: FinalizeRequestData): Promise<void> {
        const ctx = await this.loadContextForFinalize(request);
        if (request.gameFinalizationType === GameFinalizationType.ITG_FINALIZATION) {
            return this.doCompleteItgFinalization(ctx);
        } else {
            return this.doCompleteFinalization(ctx);
        }
    }

    private async doStartFinalization(request: FinalizeRequestData,
                                      lockForFinalization?: boolean): Promise<GameFlowContext> {
        const ctx = await this.loadContextForFinalize(request);
        log.info(ctx, "Finalization started");
        const paymentFinalizeType = request.finalizeBrokenPayment || FinalizeBrokenPaymentType.MARK_FINALIZED;

        if (!ctx) {
            return Promise.reject(new GameContextNotExists());
        }

        if (ctx.specialState === SpecialState.BROKEN_INTEGRATION) {
            if (this.canSkipFirstFailedBet(ctx)) {
                await ctx.rollbackPendingModification();
            }
        }

        if (ctx.brokenPayment && paymentFinalizeType === FinalizeBrokenPaymentType.DISABLE) {
            return Promise.reject(new CannotFinalizePaymentIsIncompleteError());
        }

        if (ctx.specialState !== SpecialState.FINALIZING) {
            await this.contextManager.archiveGameContext(ctx, RecoveryType.FINALIZE);
            /**
             * We set special state to forbid player to interfere the finalization process if he comes back,
             * and to state that all payments to be resolved as successful ones with flag "finalize"
             */
            if (lockForFinalization) {
                await ctx.setSpecialState(SpecialState.FINALIZING);
            }
        }

        const walletOperation = ctx?.pendingModification?.walletOperation;
        const history = ctx?.pendingModification?.history;
        /*
         When we are skipping payment we need to change roundEnded flag, but in case of simple payment retry
         round will be finished without "finalize-game"  payment. This is just additional offline retry
        */
        if (history && paymentFinalizeType === FinalizeBrokenPaymentType.MARK_FINALIZED) {
            history.roundEnded = false;
        }

        GameRecoveryService.preparePaymentForFinalization(paymentFinalizeType,
            walletOperation,
            ctx.jackpotPending,
            request.brandFinalizationType);

        if (ctx.brokenPayment) {
            await ctx.updatePending();
            if (this.isUnderRefund(ctx)) {
                await this.doFinalizeRefund(ctx);
            } else {
                await CompletionService.complete(ctx);
            }
        }

        return ctx;
    }

    private static preparePaymentForFinalization(paymentFinalizeType: FinalizeBrokenPaymentType,
                                                 walletOperation: WalletOperation,
                                                 jackpotPending: JackpotPendingModification,
                                                 finalizationType: string) {
        const jpPayment = jackpotPending?.walletOperation;
        if (paymentFinalizeType === FinalizeBrokenPaymentType.MARK_FINALIZED) {
            if (jackpotPending) {
                jackpotPending.finalizationType = finalizationType;
            }
            if (jpPayment) {
                jpPayment.finalizationType = finalizationType;
            }
            if (walletOperation) {
                walletOperation.finalizationType = finalizationType;
            }
        }
        if (walletOperation) {
            walletOperation.expireAtRevisionRequiredOnFail = true;
        }
    }

    private async doCompleteFinalization(ctx: GameFlowContext): Promise<void> {
        if (ctx && ctx.specialState === SpecialState.FINALIZING) {
            await ctx.forceCloseRound(RecoveryType.FINALIZE, ctx.gameContext);
        }
    }

    private async doCompleteItgFinalization(ctx: GameFlowContext): Promise<void> {
        if (ctx && ctx.specialState === SpecialState.FINALIZING) {
            await ctx.forceCloseRound(RecoveryType.FINALIZE, ctx.gameContext);
        }
    }

    private async getFinalizeContextUpdate(flow: EngineGameFlow<any>): Promise<ContextUpdate<GameContext>> {
        let result: ContextUpdate<GameContext>;
        if (flow.flowContext.gameContext && flow.game.finalizeGame) {
            result = await flow.game.finalizeGame(flow);
        }

        return result;
    }

    private canSkipFirstFailedBet(ctx: GameFlowContext): boolean {
        const walletOperation = ctx?.pendingModification?.walletOperation;
        return !ctx?.round?.totalEvents &&
            WalletOperationFactory.isSplitPaymentOperation(walletOperation) && !walletOperation.balanceAfterDebit;
    }

    private async getGameContextId(request: RecoveryRequestData): Promise<GameContextID | undefined> {
        if (request.gameContextId) {
            return GameContextID.createFromString(request.gameContextId);
        } else if (typeof request.round === "object") {
            const round = request.round as RoundToRecover;
            if (round.roundId && (!round.gameCode || !round.brandId || !round.playerCode || !round.device)) {
                const roundId = +round.roundId ? +round.roundId : publicId.instance.decode(round.roundId);
                const gameContext = await this.contextManager.findGameContextByRoundId(roundId);
                if (gameContext) {
                    return GameContextID.createFromString(gameContext.id);
                }
            }
            return GameContextID.create(round.gameCode, round.brandId, round.playerCode, round.device);
        } else if (typeof request.round === "string") {
            const roundId = +request.round ? +request.round : publicId.instance.decode(request.round);
            const gameContext = await this.contextManager.findGameContextByRoundId(roundId);
            if (gameContext) {
                return GameContextID.createFromString(gameContext.id);
            }
        }
    }

    private getRoundId(request: RecoveryRequestData | FinalizeRequestData): string {
        return typeof request.round === "object" ?
               (request.round as RoundToRecover).roundId : (request.round as string);
    }

    @measure({ name: "GameRecoveryService.loadGameContext", isAsync: true })
    private async loadGameContext(id: GameContextID): Promise<GameFlowContext> {
        return this.contextManager.findOrRestoreGameContext(id);
    }

    @measure({ name: "GameRecoveryService.loadContextForFinalize", isAsync: true })
    private async loadContextForFinalize(request: FinalizeRequestData): Promise<GameFlowContext> {
        let gameContextId: GameContextID;
        if (request.gameContextId) {
            gameContextId = GameContextID.createFromString(request.gameContextId);
        } else if (typeof request.round === "object") {
            const round = request.round as RoundToRecover;
            const gameMode: GameMode = PlayMode.getModeByCurrency(round.currency);
            gameContextId = GameContextID.create(round.gameCode,
                round.brandId,
                round.playerCode,
                round.device,
                gameMode);
        } else if (typeof request.round === "string") {
            const roundId = +request.round ? +request.round : publicId.instance.decode(request.round);
            const gameContext = await this.contextManager.findGameContextByRoundId(roundId);
            if (!gameContext) {
                throw new GameContextNotExists();
            }
            gameContextId = GameContextID.createFromString(gameContext.id);
        }
        const roundId = this.getRoundId(request);
        ContextVariables.setUp(gameContextId, roundId);
        return gameContextId ?
               this.contextManager.findOrRestoreGameContext(gameContextId) :
               this.contextManager.findOrRestoreGameByMerchantSessionId(request.brandId, request.merchantSessionId);

    }

    private async autoPlay(flow: EngineGameFlow,
                           ctx: GameFlowContext,
                           request: FinalizeRequestData, isItgGame = false): Promise<ContextUpdate<GameContext>> {
        const autoPlayer: AutoPlayer = await createAutoPlayer(isItgGame)(flow, ctx);
        let autoPlaySpinResult: AutoPlayResult;
        let isInstantJackpot: boolean;
        let isInstantJackpotFinished: boolean;
        do {
            autoPlaySpinResult = await autoPlayer.play(flow);
            const contextUpdate = autoPlaySpinResult.contextUpdate;
            if (contextUpdate && this.operatorSupportsFinalizationPayments(request)) {
                const pending: PreparedPendingUpdate = await flow.preparePending(contextUpdate);
                await GameRecoveryService.applyFinalizationUpdate(flow, ctx, pending, request.brandFinalizationType);
            }
            isInstantJackpot = JackpotUtil.isInstantJackpotMiniGame(ctx);
            isInstantJackpotFinished = JackpotUtil.isInstantJackpotMiniGameFinished(contextUpdate?.history);
        } while (!autoPlaySpinResult.roundEnded || (isInstantJackpot && !isInstantJackpotFinished));
        return autoPlaySpinResult?.contextUpdate;
    }

    private isUnderRefund(ctx: GameFlowContext): boolean {
        return !!ctx?.pendingModification?.walletOperation?.markForRefund;
    }

    private async doFinalizeRefund(ctx: GameFlowContext): Promise<void> {
        const flow = await GameFlowFactory.createForRecovery(ctx);
        const refund = flow.walletOperationFactory.createRefundBetOperation(ctx.pendingModification.walletOperation);
        try {
            await flow.walletManager.commitOperation(refund);
        } catch (err) {
            if (PaymentDelegate.isBrokenIntegrationError(err)) {
                await ctx.setSpecialState(SpecialState.BROKEN_INTEGRATION);
            } else if (isCannotCompletePaymentError(err)) {
                await ctx.setSpecialState(SpecialState.CANNOT_COMPLETE_PAYMENT);
            }
            throw err;
        }
    }

    private static async validateFinalizationPaymentFromGame(pendingUpdate: PreparedPendingUpdate,
                                                             ctx: GameFlowContext): Promise<void> {
        const bet = pendingUpdate?.walletOperation?.bet;
        if (bet > 0) {
            // We lock game-context. It can be force-finished or continued by player. No more finalization attempts
            await ctx.setSpecialState(SpecialState.BROKEN_GAME_CONTEXT);
            log.error("Bet attempt from finalization. Action is forbidden. " +
                "Please check game's finalization setting or game-module logic");
            throw new Errors.GameFlowIllegalInvocationException("Bet from finalization is forbidden");
        }
    }

    private checkPersistencePolicy(ctx: GameFlowContext, force: boolean, closeInSWWalletOnly: boolean) {
        if (ctx.persistencePolicy !== GameContextPersistencePolicy.NORMAL && (!force || !closeInSWWalletOnly)) {
            throw new ForbiddenToForceFinishContextError(
                "Can't force-finish or revert a game with long term persistence policy (progress will be lost)"
            );
        }
    }
}

export default new GameRecoveryService(getGameFlowContextManager());
