import * as Errors from "../errors";
import { InsufficientPlayerBalanceError } from "../errors";
import {
    IAccount,
    IChangeInfo,
    INSUFFICIENT_BALANCE,
    IOperation,
    ITransaction,
    IWallet,
    TRANSACTION_EXISTS
} from "@skywind-group/sw-wallet";
import * as wallet from "../wallet";
import { startTransaction } from "../wallet";
import { GameTokenData } from "./tokens";
import { ManagementAPISupport } from "./managementapihelper";
import config from "../config";
import { measures } from "@skywind-group/sw-utils";
import * as Big from "big.js";
import { Redis as RedisClient, ChainableCommander } from "ioredis";
import { FunRedis } from "../storage/redis";
import { ExtraData, FreeBetsInfo, GameInfo, GameWalletAction, PaymentInfo } from "@skywind-group/sw-game-core";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { deepClone } from "../utils/cloner";
import { getFunGameMultiplier } from "../utils/funGameLimits";
import { JackpotStatistic, JackpotTypeStatistic } from "./context/jpStatistics";
import { RoundStatistics } from "./context/gamecontext";
import { DeferredPayment } from "@skywind-group/sw-deferred-payment";
import { RecoveryType } from "./offlinestorage/offlineCommands";
import measure = measures.measure;
import { HistoryItemResultsData } from "@skywind-group/sw-round-details-report";

export type SplitPaymentType = "split-payment" | "bet" | "win" | "refund-bet";
export type BonusPaymentType = "redeem-bns" | "deferred-payment";
export type TransferOperationType = "transfer-in" | "transfer-out";
export type WalletOperationType =
    "payment"
    | TransferOperationType
    | "finalize-game"
    | SplitPaymentType
    | BonusPaymentType;

export interface GameJackpotWinAction extends GameWalletAction {
    change_type: "jackpot_win";
    jackpotId: string;
    pool?: string;
}

export interface SmResultTransaction {
    transactionId: string;
    type: string;
    ts: Date;
    amount: number;
}

export interface SmResultExtraData {
    gameId?: string;
    gameTitle?: string;
    lines?: number;
    transactions?: SmResultTransaction[];
}

export interface WalletOperation extends JackpotDetailsHolder, EventIdValue {
    transactionId: string;
    roundId: string;
    roundPID: string;
    gameSessionId: string;

    bet?: number;
    betBeforeWin?: number; // a bet linked with a win for split payment operation
    win?: number;
    totalBet?: number;
    totalWin?: number;

    deviceId?: string;
    operation: WalletOperationType;
    currency: string;
    retry?: number;
    roundEnded?: boolean;
    gameStatus?: string;
    finalizationType?: string;
    offlineRetry?: boolean;
    markForRefund?: boolean;
    ts?: Date;
    // if true and payment failed we need to review context expiration date and maybe update it
    expireAtRevisionRequiredOnFail?: boolean;
    smResult?: string;
    smResultExtraData?: SmResultExtraData;
    freeBetCoin?: number;
    freeBetMode?: boolean;
    roundDetails?: HistoryItemResultsData;
    gameToken?: string;
}

export class OperationType {
    public static isTransferIn(operation: WalletOperation): operation is TransferOperation {
        return operation.operation === "transfer-in";
    }

    public static isTransfer(operation: WalletOperation): operation is TransferOperation {
        return operation.operation === "transfer-in" || operation.operation === "transfer-out";
    }

    public static isRedeemBNS(operation: WalletOperation): operation is RedeemBNSOperation {
        return operation.operation === "redeem-bns";
    }

    public static isDeferredPayment(operation: WalletOperation): operation is DeferredPaymentOperation {
        return operation.operation === "deferred-payment";
    }

    public static isFinalizeGame(operation: WalletOperation): operation is FinalizeGameOperation {
        return operation.operation === "finalize-game";
    }

    public static isRefundBet(operation: WalletOperation): operation is RefundBetOperation {
        return operation.operation === "refund-bet";
    }
}

export interface EventIdValue {
    eventId?: number;
    totalEventId?: number;
}

export interface JackpotDetails {
    contributionPrecision?: number;
    jackpots: JackpotStatistic;
    jackpotTypes: JackpotTypeStatistic;
}

export interface JackpotDetailsHolder {
    totalJpContribution?: number;
    totalJpWin?: number;
    jackpotDetails?: JackpotDetails;
}

/**
 * Payment split on two phase: debit and credit
 */
export interface SplitPaymentOperation extends WalletOperation, EventIdValue, PaymentInfo, JackpotDetailsHolder {
    operation: "split-payment";
    balanceAfterDebit?: Balance;
    balanceAfterCredit?: Balance;
    debitActions?: GameWalletAction[];
    creditActions?: GameWalletAction[];
}

export interface PaymentOperation extends WalletOperation, PaymentInfo {
    operation: "payment";
    roundEnded: boolean;
    actions?: GameWalletAction[];
}

export interface JackpotWinSplitPaymentOperation extends WalletOperation, PaymentInfo {
    operation: "payment";
    actions?: GameJackpotWinAction[];
}

export interface TransferOperation extends WalletOperation, EventIdValue, TransferOut {
    operation: TransferOperationType;
    amount: number;
    betsCount?: number;
}

export interface RefundBetOperation extends WalletOperation {
    operation: "refund-bet";
    totalBet: number;
}

export interface FinalizeGameOperation extends WalletOperation {
    operation: "finalize-game";
    roundStatistics: RoundStatistics;
    recoveryType: RecoveryType;
    closeInSWWalletOnly?: boolean;
}

export interface TransferOut {
    totalBet?: number;
    totalWin?: number;
    totalJPwin?: number;
}

export interface RedeemBNSOperation extends WalletOperation, EventIdValue, PaymentInfo {
    operation: "redeem-bns";
    amount: number;
    promoId: string;
    externalId?: string;
}

export interface DeferredPaymentOperation extends WalletOperation, EventIdValue, PaymentInfo {
    operation: "deferred-payment";
    deferredPayment: DeferredPayment;
    amount: number;
    actions?: GameWalletAction[];
}

export interface ExtraBalances {
    [account: string]: number;
}

/**
 * Free bets amount and coin value.
 */
export interface FreeBets {
    amount: number;
}

export interface BonusCoinsBalance {
    amount: number;
    rewardedAmount: number;
    redeemMinAmount: number;
    redeemBalance?: number;
    redeemCurrency?: string;
    expireAt: string;
    promoId: string;
    exchangeRate: number;
    externalId?: string;
}

/**
 * Player's balance information
 */
export interface Balance {
    currency: string;
    main: number;
    previousValue?: number;
    external?: number;
    extraBalances?: ExtraBalances;
    freeBets?: FreeBets;
    bonusCoins?: BonusCoinsBalance;
    extraData?: ExtraData;
    deferredPayments?: DeferredPayment[];
}

/**
 *  Operations with player wallet
 */
export interface PlayerWalletManager {
    getBalance(internalWalletBalanceOnly?: boolean): Promise<Balance>;

    generateTransactionId(): Promise<string>;

    commitOperation(data: WalletOperation): Promise<Balance>;

    getFreeBetsInfo(gameInfo: GameInfo): Promise<FreeBetsInfo>;
}

/**
 * // TODO aguzanov we should think about getting rid of this legacy wallet
 *
 * Direct  access to player balance through 'sw-wallet' module
 */
class DirectPlayerWalletManager implements PlayerWalletManager {
    private static PLAYER_WALLET_PREFIX = "player:";
    private static PLAYER_MAIN_ACCOUNT = "main";
    private static PLAYER_BALANCE = "balance";
    private static BET_TRX_NAME = "bet";
    private static WIN_TRX_NAME = "win";

    constructor(private brandId: number,
                private playerCode: string,
                private currency: string) {
    }

    @measure({ name: "DirectPlayerWalletManager.generateTransactionId", isAsync: true })
    public generateTransactionId(): Promise<string> {
        return wallet.generateTransactionId();
    }

    @measure({ name: "DirectPlayerWalletManager.getBalance", isAsync: true })
    public async getBalance(): Promise<Balance> {
        const playerWallet: IWallet = await wallet.get(this.getWalletKey(
            this.currency));
        const account: IAccount = playerWallet.accounts.get(
            DirectPlayerWalletManager.PLAYER_MAIN_ACCOUNT);
        const main = account.get(DirectPlayerWalletManager.PLAYER_BALANCE) as number;
        const walletCurrency = Currencies.get(this.currency);

        return {
            currency: this.currency,
            main: main ? walletCurrency.toMajorUnits(main) : 0,
        };
    }

    public async getFreeBetsInfo(gameInfo: GameInfo): Promise<FreeBetsInfo> {
        return { amount: 0, coin: 0 };
    }

    @measure({ name: "DirectPlayerWalletManager.commitOperation", isAsync: true })
    public async commitOperation(data: PaymentOperation): Promise<Balance> {
        if (!(data.operation === "payment" || data.operation === "bet" || data.operation === "win")) {
            return Promise.reject(new Errors.InvalidWalletOperation());
        }

        if (Array.isArray(data.actions) && data.actions.length > 0) {
            data.bet = 0;
            data.win = 0;
            for (const action of data.actions) {
                if (action.attribute !== "balance") {
                    continue;
                }

                if (action.action === "debit") {
                    data.bet += action.amount;
                } else if (action.action === "credit") {
                    data.win += action.amount;
                }
            }
        }

        if (data.bet < 0 || data.win < 0) {
            return Promise.reject(new Errors.AmountIsNegativeError());
        }

        const trxOptions: IOperation = {
            operationId: 0
        };

        if (data.operation as string === "win") {
            trxOptions.operationId = 1;
        }

        const transaction: ITransaction = await startTransaction(data.transactionId, trxOptions);

        const walletKey = this.getWalletKey(data.currency);
        const playerWallet: IWallet = await transaction.getWallet(walletKey);
        const account: IAccount = playerWallet.accounts.get(
            DirectPlayerWalletManager.PLAYER_MAIN_ACCOUNT);
        const walletCurrency = Currencies.get(data.currency);

        if (data.bet) {
            account.inc(DirectPlayerWalletManager.PLAYER_BALANCE,
                -walletCurrency.toMinorUnits(data.bet),
                DirectPlayerWalletManager.BET_TRX_NAME,
                0);
        }

        if (data.win) {
            account.inc(DirectPlayerWalletManager.PLAYER_BALANCE,
                walletCurrency.toMinorUnits(data.win),
                DirectPlayerWalletManager.WIN_TRX_NAME,
                0);
        }

        let trxResult: IChangeInfo[];
        try {
            const commitedAt = typeof data.ts === "string" ? new Date(data.ts) : data.ts;
            trxResult = await transaction.commit(commitedAt);
        } catch (err) {
            if (err === INSUFFICIENT_BALANCE) {
                return Promise.reject(new InsufficientPlayerBalanceError());
            }
            if (err === TRANSACTION_EXISTS) {
                return this.getBalance();
            }

            return Promise.reject(err);
        }

        const state = this.findPlayerBalanceState(walletKey, trxResult);

        return {
            currency: data.currency,
            main: walletCurrency.toMajorUnits(state.currentValue),
            previousValue: walletCurrency.toMajorUnits(state.prevValue),
        };
    }

    public isPlayerBalanceChange(key: string, ci: IChangeInfo) {
        return ci.walletKey === key
            && ci.account === DirectPlayerWalletManager.PLAYER_MAIN_ACCOUNT
            && ci.property === DirectPlayerWalletManager.PLAYER_BALANCE;
    }

    public findPlayerBalanceState(walletKey,
                                  trxResult: IChangeInfo[]): { prevValue: number, currentValue: number } {
        let prevValue: number;
        let currentValue: number;
        let i = 0;

        // the fist change in the list contains previous value
        for (; i < trxResult.length; i++) {
            if (this.isPlayerBalanceChange(walletKey, trxResult[i])) {
                prevValue = trxResult[i].prevValue;
                break;
            }
        }

        // the last change in the list contains current value
        for (let j = trxResult.length - 1; j >= i; j--) {
            if (this.isPlayerBalanceChange(walletKey, trxResult[j])) {
                currentValue = trxResult[j].value;
                break;
            }
        }

        return { prevValue, currentValue };
    }

    private getWalletKey(currency: string): string {
        return DirectPlayerWalletManager.PLAYER_WALLET_PREFIX + this.brandId + ":" + this.playerCode + ":" + currency;
    }
}

export interface TransactionIdResponse {
    transactionId: string;
}

/**
 * Access to player wallet through 'sw-management-api' module
 */
export class APIPlayerWalletManager extends ManagementAPISupport implements PlayerWalletManager {
    private static readonly BALANCE_URL = "/v2/play/balance";
    private static readonly COMMIT_PAYMENT_URL = "/v2/play/payment";
    private static readonly REFUND_BET_URL = "/v2/play/payment/refund-bet";
    private static readonly TRANSFER_BALANCE_URL = "/v2/play/transfer";
    private static readonly REDEEM_COINS_URL = "/v2/play/bonusCoins";
    private static readonly DEFERRED_PAYMENT_URL = "/v2/play/payment/deferred";
    private static readonly TRX_ID_URL = "/v2/play/payment/transactionId";
    private static readonly FREE_BETS_INFO = "/v2/play/freebet";
    public static readonly FINALIZE_GAME_URL = "/v2/play/game/finalize";

    constructor(private readonly url: string,
                private readonly authToken: string,
                private readonly offlineMode: boolean = false) {
        super(url);
    }

    @measure({ name: "APIPlayerWalletManager.generateTransactionId", isAsync: true })
    public async generateTransactionId(): Promise<string> {
        const response: TransactionIdResponse =
            await this.post<TransactionIdResponse>(APIPlayerWalletManager.TRX_ID_URL, { gameToken: this.authToken });

        return response.transactionId;
    }

    @measure({ name: "APIPlayerWalletManager.getBalance", isAsync: true })
    public getBalance(internalWalletBalanceOnly: boolean = false): Promise<Balance> {
        if (internalWalletBalanceOnly) {
            return this.getInternalWalletBalanceOnly();
        }
        return this.get<Balance>(APIPlayerWalletManager.BALANCE_URL,
            { gameToken: this.authToken });
    }

    /**
     * This one is to use when only internal wallet balance is needed.
     * Example: for transfer operations that need internal balance only - without checking external (operator) balance
     */
    private getInternalWalletBalanceOnly(): Promise<Balance> {
        return this.get<Balance>(APIPlayerWalletManager.BALANCE_URL,
            { gameToken: this.authToken, onlyInternalBalance: true });
    }

    @measure({ name: "APIPlayerWalletManager.getFreeBetsInfo", isAsync: true })
    public getFreeBetsInfo(info: GameInfo): Promise<FreeBetsInfo> {
        return this.post<FreeBetsInfo>(APIPlayerWalletManager.FREE_BETS_INFO,
            {
                gameToken: this.authToken,
                coinMultiplier: info.totalBetMultiplier,
                stakeAll: info.stakeAll,
                skipCoinValidation: info.skipCoinValidation
            });
    }

    public commitOperation(data: WalletOperation): Promise<Balance> {
        if (data.operation === "payment" || data.operation === "bet" || data.operation === "win") {
            return this.commitGamePayment(data as PaymentOperation);
        } else if (OperationType.isRedeemBNS(data)) {
            return this.redeemBNS(data as RedeemBNSOperation);
        } else if (OperationType.isRefundBet(data)) {
            return this.refundBet(data as RefundBetOperation);
        } else if (OperationType.isDeferredPayment(data)) {
            return this.deferredPayment(data as DeferredPaymentOperation);
        } else if (OperationType.isFinalizeGame(data)) {
            return this.finalizeGame(data as FinalizeGameOperation);
        } else if (OperationType.isTransfer(data)) {
            return this.transfer(data as TransferOperation);
        } else {
            return Promise.reject(new TypeError("Illegal wallet operation type"));
        }
    }

    @measure({ name: "APIPlayerWalletManager.commitGamePayment", isAsync: true })
    private commitGamePayment(data: PaymentOperation): Promise<Balance> {
        const paymentData = this.clonePaymentOperation(data);
        this.checkOfflineMode(paymentData);
        if (!paymentData.gameToken) {
            paymentData.gameToken = this.authToken;
        }
        return this.put<Balance>(APIPlayerWalletManager.COMMIT_PAYMENT_URL, paymentData);
    }

    @measure({ name: "APIPlayerWalletManager.transfer", isAsync: true })
    private transfer(data: TransferOperation): Promise<Balance> {
        const transferData: any = deepClone(data);
        transferData.gameToken = this.authToken;
        transferData.ts = data.ts;
        this.checkOfflineMode(transferData);
        return this.put<Balance>(APIPlayerWalletManager.TRANSFER_BALANCE_URL, transferData);
    }

    @measure({ name: "APIPlayerWalletManager.redeemBNS", isAsync: true })
    private redeemBNS(data: RedeemBNSOperation): Promise<Balance> {
        const redeemData: any = deepClone(data);
        redeemData.gameToken = this.authToken;
        redeemData.ts = data.ts;
        this.checkOfflineMode(redeemData);
        return this.post<Balance>(APIPlayerWalletManager.REDEEM_COINS_URL, redeemData);
    }

    @measure({ name: "APIPlayerWalletManager.deferredPayment", isAsync: true })
    private deferredPayment(data: DeferredPaymentOperation): Promise<Balance> {
        const redeemData: any = deepClone(data);
        redeemData.gameToken = this.authToken;
        redeemData.ts = data.ts;
        this.checkOfflineMode(redeemData);
        return this.put<Balance>(APIPlayerWalletManager.DEFERRED_PAYMENT_URL, redeemData);
    }

    @measure({ name: "APIPlayerWalletManager.refundBet", isAsync: true })
    private refundBet(data: RefundBetOperation): Promise<Balance> {
        const refundData: any = deepClone(data);
        refundData.ts = data.ts;
        this.checkOfflineMode(refundData);
        if (!refundData.gameToken) {
            refundData.gameToken = this.authToken;
        }
        return this.delete<Balance>(APIPlayerWalletManager.REFUND_BET_URL, refundData);
    }

    @measure({ name: "APIPlayerWalletManager.finalizeGame", isAsync: true })
    private finalizeGame(data: FinalizeGameOperation): Promise<Balance> {
        const finalizeData: any = deepClone(data);
        finalizeData.ts = data.ts;
        this.checkOfflineMode(finalizeData);
        finalizeData.gameToken = this.authToken;
        return this.post<Balance>(APIPlayerWalletManager.FINALIZE_GAME_URL, finalizeData);
    }

    private clonePaymentOperation(data: PaymentOperation): PaymentOperation {
        const result: PaymentOperation = deepClone(data);
        result.bet = undefined;
        result.win = undefined;
        result.ts = data.ts;
        if (result.smResultExtraData && result.smResultExtraData.transactions) {
            const dataTransactions = data.smResultExtraData.transactions;
            const resultTransactions = result.smResultExtraData.transactions;
            for (let i = 0; i < resultTransactions.length; i++) {
                resultTransactions[i].ts = dataTransactions[i].ts;
            }
        }

        return result;
    }

    private checkOfflineMode(data) {
        if (this.offlineMode) {
            data.offlineRetry = true;
        }
    }
}

/**
 * Access to player wallet for fun mode
 */
class FunPlayerWalletManager implements PlayerWalletManager {
    private walletKey: string;

    constructor(brandId: number,
                playerCode: string,
                private currency: string,
                private startAmount: number = config.funGame.startAmount) {
        this.walletKey = config.funGame.redis.keyPrefix + ":wallet:" + brandId + ":" + playerCode + ":" + currency;
    }

    public async generateTransactionId(): Promise<string> {
        return String(Date.now());
    }

    @measure({ name: "FunPlayerWalletManager.getBalance", isAsync: true })
    public async getBalance(): Promise<Balance> {
        const client: RedisClient = await FunRedis.get().get();
        await client.watch(this.walletKey);
        try {
            let balanceValue = await this.getBalanceByKey(client);
            if (balanceValue === undefined) {
                const defaultBalance = Math.round(this.startAmount * getFunGameMultiplier(this.currency));
                balanceValue = String(defaultBalance);
                await this.modifyBalance(client, balanceValue);
            }
            return {
                currency: this.currency,
                main: Number(balanceValue),
            };
        } finally {
            await client.unwatch();
            await FunRedis.get().release(client);
        }
    }

    public async getFreeBetsInfo(gameInfo: GameInfo): Promise<FreeBetsInfo> {
        return { amount: 0, coin: 0 };
    }

    @measure({ name: "FunPlayerWalletManager.commitOperation", isAsync: true })
    public async commitOperation(data: PaymentOperation): Promise<Balance> {
        if (data.operation !== "payment") {
            return Promise.reject(new Errors.InvalidWalletOperation());
        }

        if (data.bet < 0 || data.win < 0) {
            return Promise.reject(new Errors.AmountIsNegativeError());
        }

        const client: RedisClient = await FunRedis.get().get();
        // we need to watch key, because we need consistent modification
        client.watch(this.walletKey);
        try {
            const value = await this.getBalanceByKey(client);
            if (value === undefined) {
                return Promise.reject(new Errors.FunGameSessionExpired());
            }
            let balance = new Big(value);
            if (balance.lt(0)) {
                return Promise.reject(new InsufficientPlayerBalanceError());
            }

            if (data.bet) {
                balance = balance.minus(new Big(data.bet));
                if (balance.lt(0)) {
                    return Promise.reject(new InsufficientPlayerBalanceError());
                }
            }

            if (data.win) {
                balance = balance.plus(new Big(data.win));
            }

            await this.modifyBalance(client, String(balance));

            return {
                currency: data.currency,
                main: Number(balance),
                previousValue: Number(value),
            };
        } finally {
            await client.unwatch();
            await FunRedis.get().release(client);
        }
    }

    private async getBalanceByKey(client: RedisClient): Promise<string> {
        const response = await client.get(this.walletKey);
        return response || undefined;
    }

    private async modifyBalance(client: RedisClient, balance: string): Promise<void> {
        const multi: ChainableCommander = client.multi();
        try {
            multi.setex(this.walletKey, config.funGame.TTL, balance);
        } catch (err) {
            multi.discard();
            return Promise.reject(err);
        }
        const response = await multi.exec();

        if (!response) {
            return Promise.reject(new Error("inconsistent modification"));
        }
    }
}

export function createWalletManager(tokenData: GameTokenData, offlineMode: boolean = false): PlayerWalletManager {
    if (tokenData.playmode === "fun") {
        return new FunPlayerWalletManager(
            tokenData.brandId,
            tokenData.playerCode,
            tokenData.currency,
            tokenData.defaultBalance);
    } else {
        if (config.walletThroughAPI) {
            return new APIPlayerWalletManager(config.managementAPI.url, tokenData.token, offlineMode);
        } else {
            return new DirectPlayerWalletManager(tokenData.brandId, tokenData.playerCode, tokenData.currency);
        }
    }
}
