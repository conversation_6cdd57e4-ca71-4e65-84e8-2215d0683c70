import * as Errors from "../../errors";
import { JackpotR<PERSON>ult, TickerResponse, TickersResponse } from "@skywind-group/sw-game-core";
import { getJPNServer } from "./jpnserver";
import {
    AuthRequest,
    AuthResponse as JPNAuthResponse,
    CheckWinRequest,
    ContributionEvent,
    ContributionRequest,
    JackpotEvent,
    JackpotShortInfo,
    JackpotWinEvent,
    MiniGameRequest,
    WinConfirmRequest,
    WinJackpotRequest
} from "@skywind-group/sw-jpn-core";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { GameData } from "../auth";
import { PlayMode } from "../playMode";

export interface JackpotContext {
    token: string;
    jackpotsInfo: JackpotInfo[];
    readonly contributionEnabled?: boolean;
    readonly jackpot?: JackpotTrxResult;
    instantJackpotAwarded?: boolean;
}

export interface ContributionProcessResult {
    ticker?: TickerResponse;
    contribution: number;
    result: ContributionResult[];
}

export interface WinProcessResult {
    win: number;
    exponent: number;
    seed?: number;
    progressive?: number;
}

export interface Jackpot {
    readonly jpInfo: JackpotInfo;

    processContributionEvent(event: JackpotEvent): ContributionProcessResult;

    processWinEvent(event: JackpotWinEvent): WinProcessResult;
}

export interface JackpotSettings {
    id: string;
    allowMissingContribution?: boolean;
    isGameOwned: boolean;
    gameHistoryEnabled: boolean;
    paymentStatisticEnabled: boolean;
    winPaymentType?: string;

    [setting: string]: any;
}

export interface JackpotInfo extends JackpotShortInfo, JackpotSettings {
}

export type JackpotOperationType =
    "contribution"
    | "check-win"
    | "mini-game"
    | "win-confirm"
    | "win-rollback"
    | "win-jackpot";

export interface JackpotOperation {
    type: JackpotOperationType;
    payload: ContributionRequest | CheckWinRequest | MiniGameRequest | WinConfirmRequest | WinJackpotRequest;
    previousResult?: JackpotOperationResult;
}

export interface JackpotTrxResult {
    transactionId: string;
    result: JackpotResult[];
}

export interface ContributionResult {
    jackpotId: string;
    pool: string;
    seed?: number;
    progressive: number;
}

export interface JackpotOperationResult {
    jackpot?: JackpotTrxResult;
    tickers?: TickersResponse;
    totalJpContribution?: number;
    totalJpWin?: number;
    contributionResult?: ContributionResult[];
    seedWin?: number;
    progressiveWin?: number;
}

export class JackpotImpl implements Jackpot {

    public static findById(context: JackpotContext, jackpotId: string): Jackpot {
        return new JackpotImpl(context.jackpotsInfo.find((jp) => jp.id === jackpotId));
    }

    constructor(public readonly jpInfo: JackpotInfo) {
    }

    public processContributionEvent(event: JackpotEvent): ContributionProcessResult {
        const contributionEvent = (event as ContributionEvent);
        let ticker: TickerResponse;
        if (this.jpInfo.isGameOwned) {
            ticker = {
                jackpotId: event.jackpotId,
                jackpotType: event.jackpotType,
                jackpotBaseType: event.jackpotBaseType,
                pools: contributionEvent.pools,
            } as any;
        }
        let totalContribution: number;
        if (this.jpInfo.gameHistoryEnabled) {
            totalContribution = contributionEvent.totalContribution;
        }
        const contributionResult = [];
        if (contributionEvent.contributions) {
            for (const contribution of contributionEvent.contributions) {
                contributionResult.push({
                    jackpotId: contributionEvent.jackpotId,
                    pool: contribution.pool,
                    seed: contribution.seed,
                    progressive: contribution.progressive
                });
            }
        }
        return { ticker, contribution: totalContribution, result: contributionResult };
    }

    public processWinEvent(event: JackpotWinEvent): WinProcessResult {
        let win;
        let exponent;
        if (this.jpInfo.gameHistoryEnabled) {
            if (event.amount) {
                win = event.amount;
                exponent = Currencies.get(this.jpInfo.currency).exponent;
            }
        }
        const result: WinProcessResult = {
            win: win,
            exponent: exponent,
            progressive: event.progressive,
            seed: event.seed,
        };

        return result;
    }

}

export async function initJackpot(gameData: GameData): Promise<[JackpotContext, JackpotShortInfo[]]> {
    if (!PlayMode.supportJP(gameData?.gameTokenData?.playmode)) {
        return [undefined, undefined];
    }

    const jackpots: JackpotSettings[] = gameData.jackpots;
    if (!jackpots || !jackpots.length) {
        return [undefined, undefined];
    }

    let jpnContext: JackpotContext;
    let gameJackpotInfo: JackpotShortInfo[];

    const authRequest: AuthRequest = {
        playerCode: gameData.gameTokenData.playerCode,
        brandId: gameData.gameTokenData.brandId,
        currency: gameData.gameTokenData.currency,
        jackpotIds: jackpots.map((jp) => jp.id),
        gameCode: gameData.gameTokenData.gameCode,
        region: gameData.region,
        includeDisabled: true,
        autoCreateTestJackpot: gameData.settings.autoCreateTestJackpot,
        isTest: gameData.gameTokenData.test
    };

    if (gameData.gameTokenData.nickname) {
        authRequest.nickname = gameData.gameTokenData.nickname;
    }

    const jpnAuth: JPNAuthResponse = await getJPNServer().auth(authRequest);

    if (jpnAuth) {
        checkJackpotsAreTest(jpnAuth.jackpots, gameData.gameTokenData.test);

        gameJackpotInfo = [];
        const allJackpots: JackpotInfo[] = [];

        for (const jp of jpnAuth.jackpots) {
            const jpSettings = jackpots.find((item) => (item.id === jp.id || item.id === jp.createdFromId));

            if (jp.isDisabled) {
                if (jpSettings.isGameOwned) {
                    return Promise.reject(new Errors.JackpotDisabledError());
                } else if (jpSettings.type !== "instant") {
                    continue;
                }
            }

            if (jpSettings.isGameOwned) {
                gameJackpotInfo.push(jp);
            }

            allJackpots.push({ ...jpSettings, ...jp });
        }

        jpnContext = {
            token: jpnAuth.token,
            jackpotsInfo: allJackpots,
            contributionEnabled: true,
        };
    }

    return [jpnContext, gameJackpotInfo];
}

function checkJackpotsAreTest(jackpots: JackpotShortInfo[], isPlayerTest: boolean) {
    if (isPlayerTest && !jackpots.every(jackpot => jackpot.isTest)) {
        throw new Errors.JackpotTestModeError();
    }
    if (!isPlayerTest && jackpots.some(jackpot => jackpot.isTest)) {
        throw new Errors.JackpotRealModeError();
    }
}
