import {
    ManagementAPIBrokenIntegration,
    ManagementAPIConnectionError,
    ManagementAPIError,
    ManagementAPITransientError,
    TransactionIsInProgressError
} from "../errors";
import { keepalive, logging, retry } from "@skywind-group/sw-utils";
import config from "../config";
import * as request from "superagent";
import { retryCondition } from "../utils/common";
import HttpsAgent = keepalive.HttpsAgent;

const log = logging.logger("sw-slot-engine:management-api-support");

const agent = keepalive.createAgent(config.managementAPI.keepAlive, config.managementAPI.url) as HttpsAgent;

export class ManagementAPISupport {

    private static readonly SUCCESSFUL_CODES = [200, 201, 202, 204];
    private static readonly TRANSACTION_IS_PROCESSING_CODE = 668;

    constructor(private baseURL: string) {
    }

    protected async post<T>(url: string, req, ip?: string, cookies?: string, query?): Promise<T> {
        const requestLog = this.createRequestLog(url, req, "post", ip);

        log.debug({ requestSupport: requestLog }, "Request");

        return retry(config.retries, async () => {
            return this.processResponse(
                request
                    .post(`${this.baseURL}${url}`)
                    .agent(agent)
                    .query(query)
                    .set(this.getHeaders(ip, cookies))
                    .send(req));
        }, retryCondition);
    }

    protected async get<T>(url: string, req): Promise<T> {
        const requestLog = this.createRequestLog(url, req, "get");

        log.debug({ requestSupport: requestLog }, "Request");

        return retry(config.retries, async () => {
            return this.processResponse(
                request
                    .get(`${this.baseURL}${url}`)
                    .agent(agent)
                    .query(req)
                    .set(this.getHeaders()));
        }, retryCondition);
    }

    protected async put<T>(url: string, req): Promise<T> {
        const requestLog = this.createRequestLog(url, req, "put");

        log.debug({ requestSupport: requestLog }, "Request");

        return retry(config.retries, async () => {
            return this.processResponse(
                request
                    .put(`${this.baseURL}${url}`)
                    .agent(agent)
                    .set(this.getHeaders())
                    .send(req));
        }, retryCondition);
    }

    protected async delete<T>(url: string, req, ip?: string, cookies?: string, query?): Promise<T> {
        const requestLog = this.createRequestLog(url, req, "delete", ip);

        log.debug({ requestSupport: requestLog }, "Request");

        return retry(config.retries, async () => {
            return this.processResponse(
                request
                    .del(`${this.baseURL}${url}`)
                    .agent(agent)
                    .query(query)
                    .set(this.getHeaders(ip, cookies))
                    .send(req));
        }, retryCondition);
    }

    protected async processResponse(result: request.SuperAgentRequest): Promise<any> {
        try {
            const response = await result;
            return this.process(response);
        } catch (error) {
            if (error.response) {
                return this.process(error.response, error);
            } else {
                log.warn(error, "Network error during request");
                return Promise.reject(new ManagementAPIConnectionError(error));
            }
        }
    }

    private process(response: request.Response, error?: Error): Promise<any> {
        if (error && !response) {
            log.warn(error, "Error sending request");
            return Promise.reject(new ManagementAPIConnectionError(error));
        }
        if (!ManagementAPISupport.SUCCESSFUL_CODES.includes(response.status)) {
            log.warn({ responseCode: response.status, responseBody: response.body }, "Error response");
            if (response.status >= 400 && response.status < 500) {
                return Promise.reject(new ManagementAPIError(response.status, response.body));
            } else if (response.status === 501) {
                return Promise.reject(new ManagementAPIBrokenIntegration(response.status, response.body));
            } else if (this.isTransactionInProgress(response)) {
                return Promise.reject(new TransactionIsInProgressError(response.status, response.body));
            } else {
                return Promise.reject(new ManagementAPITransientError(response.status, response.body));
            }
        } else {
            const path = (response as any)?.req?.path;
            const method = (path && path.includes("/transactionId")) ? "debug" : "info";
            log[method]({ responseCode: response.status, responseBody: response.body }, "Response");
            return this.parseData(response, response.body);
        }
    }

    private isTransactionInProgress(response) {
        return response.status === 500 &&
            response?.body?.code === ManagementAPISupport.TRANSACTION_IS_PROCESSING_CODE;
    }

    protected parseData(response: request.Response, body: any): any {
        return body;
    }

    protected createRequestLog(url, reqData, method, ip?) {
        return {
            url,
            method: method,
            baseUrl: this.baseURL,
            request: reqData,
            ip
        };
    }

    protected getHeaders(ip?: string, cookies?: string) {
        const headers: any = {
            "Content-Type": "application/json",
            "X-GS": config.environmentId || ""
        };
        if (ip) {
            headers["x-forwarded-for"] = ip;
        }

        if (cookies) {
            headers["Cookie"] = cookies;
        }

        return headers;
    }
}
