import { ContextManager } from "./contextmanager/contextManager";
import { logging, measures } from "@skywind-group/sw-utils";
import config from "../config";
import { DBContext } from "./offlinestorage/offlineCommands";
import { ManagementAPISupport } from "./managementapihelper";
import { ExpireGameRequestData } from "./gamerecovery";
import { generateInternalToken } from "./tokens";
import measureProvider = measures.measureProvider;
import { ContextVariables } from "../utils/contextVariables";
import { isOperationForbiddenError } from "../errors";
import { GameContextID } from "./contextIds";
import { ContextFinalizationTimeCalculator } from "./contextFinalizationTimeCalculator";
import { GameFlowContext } from "./context/gamecontext";
import { GameFlowContextImpl } from "./context/gameContextImpl";

const log = logging.logger("skywind:slot-engine:expire-context");

export class ExpireContextJob extends ManagementAPISupport {
    public static readonly EXPIRE_GAME_URL = "/expire-game";

    constructor(private readonly manager: ContextManager) {
        super(config.managementAPI.internalServerUrl);
    }

    public async fire() {
        const cfg = config.expireJob;
        while (await this.doWork(cfg.batch)) {
            log.debug("Processed %s items", cfg.batch);
        }
    }

    public async doWork(batchSize: number): Promise<boolean> {
        const items = await this.manager.findExpiredGameContext(new Date(), batchSize);

        await Promise.all(items.map(ctx => this.finalizeRequest(ctx)));

        return items.length >= batchSize;
    }

    private async finalizeRequest(ctx: DBContext): Promise<void> {
        const request: ExpireGameRequestData = {
            gameContextId: ctx.id,
            brandId: ctx.brandId,
            gameCode: ctx.gameCode,
            roundId: ctx.roundId,
            playerCode: ctx.playerCode
        };
        return measureProvider.runInTransaction("Expire game context", async () => {
            try {
                const round = JSON.parse(ctx?.data?.round || null);
                ContextVariables.setUp(ctx, round?.roundId);
                const token = await generateInternalToken(request);
                await this.post(ExpireContextJob.EXPIRE_GAME_URL, { token });
            } catch (err) {
                measureProvider.saveError(err);
                log.error(err, "Failed to finalize context", request);
                if (isOperationForbiddenError(err)) {
                    const result = await this.manager.updateExpirationDate(ctx, null);
                    log.warn({
                        id: ctx.id,
                        updated: Array.isArray(result) && result[0]
                    }, "Received forbidden error. Clear expiredAt.");
                    return;
                }
                const gameFlowContext = await this.getGameFlowContext(ctx);
                const nextExpiredAt = ContextFinalizationTimeCalculator.getNextExpiredAt(gameFlowContext);
                const nextExpiredAtDate = nextExpiredAt ? new Date(nextExpiredAt) : null;
                const result = await this.manager.updateExpirationDate(ctx, nextExpiredAtDate);
                if (!nextExpiredAtDate) {
                    log.warn({
                        id: ctx.id,
                        updated: Array.isArray(result) && result[0]
                    }, "Max finalization retries reached. Clear expiredAt.");
                } else {
                    log.warn({
                        id: ctx.id,
                        newExpiredAt: nextExpiredAtDate.toISOString(),
                        updated: Array.isArray(result) && result[0]
                    }, "Prolong expiredAt");
                }
            }
        });
    }

    private async getGameFlowContext(ctx: DBContext): Promise<GameFlowContext> {
        return new GameFlowContextImpl(GameContextID.createFromString(ctx.id));
    }
}
