import { logging, measures } from "@skywind-group/sw-utils";
import { BaseRequest, ClientResponse, GameEvent, GamePlayRequest } from "@skywind-group/sw-game-core";
import { AsyncGameController, createAsyncGameController } from "./asynccontroller";
import * as Errors from "../errors";
import {
    ConcurrentAccessToGameSession,
    DuplicationOfInitRequestError,
    isCannotCompletePaymentError,
    isInterruptSocketError,
} from "../errors";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { GameFlowContext, RequestContext, RequestReferrer } from "./context/gamecontext";
import { logAndGetSWError } from "../api/middleware";
import { ContextVariables } from "../utils/contextVariables";
import measureProvider = measures.measureProvider;
import Metrics = measures.Metrics;

const log = logging.logger("sw-slot-engine:io");
/**
 * Implementation of queue.
 * Because we use socket transport in fish game, we can get simultaneous fire request before we have processed
 * previous.
 * This queue help us to aggregate unprocessed fire requests.
 */
class RequestQueue {
    private queue: RequestQueueItem[] = [];
    private offset: number = 0;

    public enqueue(item: RequestQueueItem): RequestQueueItem {
        this.queue.push(item);
        return item;
    }

    public dequeue(): RequestQueueItem {
        if (this.queue.length === 0) {
            return undefined;
        }

        const item = this.queue[this.offset];

        if (++this.offset * 2 >= this.queue.length) {
            this.queue = this.queue.slice(this.offset);
            this.offset = 0;
        }
        return item;
    }

    public isEmpty() {
        return (this.queue.length === 0);
    }

    public clear(): void {
        this.offset = 0;
        this.queue = [];
    }
}

interface RequestQueueItem {
    asyncRequest: AsyncRequest;
    callback: RequestCallback;
    internal?: boolean;
}

type RequestCallback = (err, response?) => Promise<void>;

class RequestProcessor {
    private queue: RequestQueue = new RequestQueue();
    private processing: boolean;
    private finished: boolean;
    private finishResolver: (err?) => void;
    private lastRequestInQueue: string;
    private static REQUEST_TO_IGNORE = ["jp-ticker", "balance"];

    constructor(private controller: AsyncGameController) {
    }

    public getSessionInfo(parsed: boolean = true): any {
        const id = this.controller?.gameFlowContext?.id;
        if (parsed) {
            return {
                brandId: id?.brandId,
                playerCode: id?.playerCode,
                deviceId: id?.deviceId,
                gameCode: id?.gameCode
            };
        }
        return id && id.asString();
    }

    public get gameFlowContext(): GameFlowContext {
        return this.controller.gameFlowContext;
    }

    public process(asyncRequest: AsyncRequest, callback: RequestCallback, internal: boolean) {
        if (this.finished && !internal) {
            return callback(new Errors.GameSessionClosed());
        }
        return this.processEnqueued(asyncRequest, callback, internal);
    }

    public async finish(waitForFinishProcessing: boolean = true, req?: AsyncRequest): Promise<void> {
        if (this.finished) {
            throw new Errors.GameSessionClosed();
        }
        this.finished = true;
        if (waitForFinishProcessing && this.processing) {
            log.info("Wait for queue to be processed");
            await new Promise<void>((resolve, reject) => {
                if (!this.processing) {
                    return resolve();
                }
                this.finishResolver = (err?) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve();
                    }
                };
            });
        } else {
            /* In case of when we have something in request queue all next requests will be failed with errors
            and a lot of duplicated errors will be in logs in case of long queue
            */
            this.queue.clear();
        }
        /*
        Flag to prevent concurrent pending payment commit.
        When we already waited for processing finished concurrency is impossible, because processing is finished.
        When nothing is processing now concurrency is also impossible because previous request already processed and
        next will not be processed because finished flag.
        */
        const checkPendingOnClose: boolean = waitForFinishProcessing || !this.processing;
        if (!checkPendingOnClose) {
            log.info("Async connection: check pending skipped on close, because of potential concurrency problem");
        }
        await this.controller.finish(checkPendingOnClose, req);
    }

    public isFinished(): boolean {
        return this.finished;
    }

    private async processImmediate(item: RequestQueueItem): Promise<void> {
        return item.asyncRequest.continueTransaction(
            async () => {
                ContextVariables.setUpWithContext(this.controller?.gameFlowContext);
                try {
                    const response = await this.processRequest(item.asyncRequest.baseRequest, item.internal);
                    await item.callback(undefined, response);
                } catch (err) {
                    try {
                        await item.callback(err);
                    } catch (callbackErr) {
                        log.error(callbackErr, "Error in callback after error");
                    }
                } finally {
                    this.gaugeWebsocketMetrics(item);
                }
            });
    }

    private gaugeWebsocketMetrics(item: RequestQueueItem) {
        measureProvider.incrementGauge(Metrics.WEBSOCKET_REQUESTS_DURATION,
            (Date.now() - item.asyncRequest.requestTs) / 1000);
        measureProvider.incrementGauge(Metrics.WEBSOCKET_REQUESTS_COUNT, 1);
    }

    private async processEnqueued(asyncRequest: AsyncRequest, callback: RequestCallback, internal): Promise<void> {
        // enqueue request if we are still processing previous fire
        const item = { asyncRequest, callback, internal };
        if (this.processing) {
            /*
            Sometimes the wrapper gets mad and sends balance/jp-ticker request each 2-5 milliseconds.
            Each get-balance request calls mapi and merchant's adapter. This is like DoS attack.
            To protect from this we will ignore part of these requests.
            If last request in queue the same as current, there is no sense to handle both requests.
            */
            const requestType = asyncRequest.baseRequest.request;
            if (!this.queue.isEmpty() && RequestProcessor.REQUEST_TO_IGNORE.includes(requestType)
                && this.lastRequestInQueue === requestType) {
                log.info("Ignore consecutive duplicate request " + requestType);
                return;
            }
            this.queue.enqueue(item);
            this.lastRequestInQueue = requestType;
        } else {
            let error;
            try {
                this.processing = true;
                await this.processImmediate(item);

                while (!this.queue.isEmpty()) {
                    const next = this.queue.dequeue();
                    await this.processImmediate(next);
                }
            } catch (err) {
                error = err;
                throw err;
            } finally {
                this.processing = false;

                if (this.finished && this.finishResolver) {
                    this.finishResolver(error);
                }
            }
        }
    }

    private async processRequest(request: BaseRequest, internal?: boolean): Promise<ClientResponse> {
        return internal ? this.controller.internalEvent(request) : this.controller.process(request);
    }
}

export interface AsyncGameClient {
    sendResponse(message: string, res: ClientResponse);

    sendEvent(event: GameEvent);

    sendError(err: SWError);

    disconnect();
}

export class AsyncRequest {
    public readonly continueTransaction: <T>(action: () => T) => T;
    public readonly requestTs = Date.now();

    constructor(public readonly baseRequest: BaseRequest & RequestContext & RequestReferrer) {
        this.continueTransaction = measures.measureProvider
            .trackTransaction<any>(this.doContinueTransaction.bind(this));
    }

    public static createRequest(baseRequest: BaseRequest & RequestContext & RequestReferrer): AsyncRequest {
        return new AsyncRequest(baseRequest);
    }

    public static startTransaction(handler: (req: BaseRequest & RequestContext & RequestReferrer) => any) {
        return (req: BaseRequest) => {
            return measureProvider.runInTransaction(req.request, () => {
                measureProvider.setTransaction(req.request);
                return handler(req);
            });
        };
    }

    private doContinueTransaction<T>(action: () => T): T {
        return action();
    }
}

export class AsyncGameConnection {
    private processor: RequestProcessor;

    constructor(private client: AsyncGameClient) {
    }

    public init(message: string, baseRequest: BaseRequest & RequestContext & RequestReferrer) {
        const request = AsyncRequest.createRequest(baseRequest);
        if (this.processor) {
            return this.processError(new DuplicationOfInitRequestError(), request);
        }

        log.info({ requestIo: request.baseRequest }, "SocketIO init");
        const conn = this;
        const controller = createAsyncGameController({
            notifyPlayer(event: GameEvent): Promise<void> {
                conn.client.sendEvent(event);
                return;
            },
            notifyError(err: Error): Promise<void> {
                return conn.processError(err, request);
            },

            scheduleInternalEvent(playRequest: GamePlayRequest): void {
                conn.request(playRequest.request, playRequest, true);
            }
        });

        this.processor = new RequestProcessor(controller);

        this.processRequest(message, request);
    }

    public request(message: string, request: BaseRequest, internal?: boolean) {
        this.processRequest(message, AsyncRequest.createRequest(request), internal);
    }

    public async disconnect(request?: AsyncRequest): Promise<void> {
        try {
            if (this.processor) {
                log.info(this.processor.getSessionInfo(), "player disconnected");
                return await this.processDisconnect(true, request);
            }
        } catch (err) {
            log.error(err, "Error on processing disconnect");
            // if there is a ConcurrentAccessToGameSession error here, the player might have already
            // refreshed the game before we processed the disconnect event
            if (err instanceof ConcurrentAccessToGameSession) {
                log.error(
                    err,
                    "Concurrent access error was caught. Attempt disconnect without waiting for processing."
                );
                return this.processDisconnect(false, request);
            }
            throw err;
        }
    }

    private processRequest(message: string, request: AsyncRequest, internal?: boolean) {
        if (!this.processor || (this.processor.isFinished() && !internal)) {
            log.info({ requestIo: request.baseRequest }, "SocketIO message:%s <<< skipped", message);
        } else {
            const session = this.processor.getSessionInfo(false);
            ContextVariables.setUp(session, undefined, this.processor?.gameFlowContext?.session?.sessionId);

            log.info({ requestIo: request.baseRequest, session: session },
                "SocketIO message:%s >>>",
                message);
            this.processor.process(request, async (err, response) => {
                if (err) {
                    return this.processError(err, request);
                } else {
                    log.info({
                        requestIo: request.baseRequest,
                        responseIo: response,
                        session: session
                    }, "SocketIO:%s <<<", message);
                    this.client.sendResponse(message, response);
                }
            }, internal);
        }
    }

    private async processError(err, req: AsyncRequest) {
        await this.client.sendError(logAndGetSWError(err, log, req.baseRequest));
        if (!Errors.isSWError(err)
            // reject concurrent usage
            || (err instanceof Errors.ConcurrentAccessToGameSession)
            // reject broken context
            || (err instanceof Errors.GameContextStateIsBroken)
            // check if token expired
            || (err instanceof Errors.ManagementAPIError && err.code === 323)
            // check if game duplicate init request for the same socket
            || (err instanceof Errors.DuplicationOfInitRequestError)
            // when InterruptSocketError error occurs, break the socket (retries could work)
            || (isInterruptSocketError(err))
            // when CannotCompletePayment error occurs, break the socket (payment retries will be disabled)
            || (isCannotCompletePaymentError(err))
        ) {
            try {
                await this.processDisconnect(false, req);
            } finally {
                log.info({ error: err, message: "Disconnect on error" });
                this.client.disconnect();
            }
        }
    }

    private async processDisconnect(waitForProcessing: boolean = true, req?: AsyncRequest) {
        if (this.processor && !this.processor.isFinished()) {
            if (req) {
                await req.continueTransaction(async () => {
                    await this.processor.finish(waitForProcessing, req);
                });
            } else {
                await measureProvider.runInTransaction("process-disconnect", async () => {
                    await this.processor.finish(waitForProcessing, req);
                });
            }
            this.processor = undefined;
        }
    }
}
