import { lazy } from "@skywind-group/sw-utils";
import { Suite } from "benchmark";

const msgpack = lazy(() => require("msgpack"));
const suite = new Suite("Test cloning", { minSamples: 200 });

// tslint:disable-next-line:max-line-length
const ctxAsString = "{\"gameData\":{\"gameTokenData\":{\"token\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbiI6IiIsImdhbWVDb2RlIjoic3dfYWwiLCJwbGF5ZXJDb2RlIjoic29tZV9wbGF5ZXJfaWRlbnRpZmllciIsImJyYW5kSWQiOjEwMCwiY3VycmVuY3kiOiJVU0QiLCJ0cmFuc2ZlckVuYWJsZWQiOmZhbHNlLCJ0ZXN0IjpmYWxzZX0.aQIDdUdKCmMYDCkyv_44UHY-HgCZt-mwZg2KlGaCaug\",\"gameCode\":\"sw_al\",\"playerCode\":\"player00000000000056\",\"brandId\":100,\"currency\":\"USD\",\"transferEnabled\":false,\"test\":false},\"limits\":{\"coins\":[1],\"defaultCoin\":1,\"maxTotalStake\":500,\"stakeAll\":[0.1,0.5,1,2,3,5],\"stakeDef\":1,\"stakeMax\":10,\"stakeMin\":0.1,\"winMax\":3000000,\"currencyMultiplier\":100},\"jrsdSettings\":{\"showRTP\":true,\"rulesDateStamped\":true,\"autoPlayUntilFeatureEnabled\":true,\"autoPlayLimitEnabled\":true,\"autoPlayLossLimitEnabled\":true,\"autoPlayLossLimitDefaultValue\":500,\"autoPlaySingleWinLimitEnabled\":true,\"autoPlaySingleWinLimitDefaultValue\":1000,\"stopAutoPlayOnJP\":true,\"showClockOnMobile\":true,\"noAdaptiveProbability\":true},\"gameMode\":\"real\"},\"gameVersion\":\"0.3.0\",\"gameContext\":{\"currentScene\":\"main\",\"scenesState\":{\"main\":{\"multiplier\":1,\"behaviorsState\":{\"regularSlotSceneBehavior\":null}},\"optionsSelector\":{\"multiplier\":1,\"initialFreeSpinWin\":12,\"currentRoundId\":0,\"bonusHistory\":[{\"id\":0,\"selectedIds\":[1],\"items\":[{\"id\":0,\"available\":false,\"visibleValue\":\"10\"},{\"id\":1,\"available\":false,\"visibleValue\":\"1\"},{\"id\":2,\"available\":false,\"visibleValue\":\"7\"},{\"id\":3,\"available\":false,\"visibleValue\":\"3\"},{\"id\":4,\"available\":false,\"visibleValue\":\"12\"},{\"id\":5,\"available\":false,\"visibleValue\":\"9\"}]}],\"selectedIds\":[2]},\"freeSpinsSet2\":{\"multiplier\":1,\"behaviorsState\":{\"freeGamesSlotSceneBehavior\":{\"freeSpinsCount\":0,\"initialFreeSpinsCount\":0,\"totalFreeSpinsCount\":0,\"freeSpinsWin\":0,\"initialFreeSpinWin\":0}}},\"freeSpinsSet1\":{\"multiplier\":1,\"behaviorsState\":{\"freeGamesSlotSceneBehavior\":{\"freeSpinsCount\":0,\"initialFreeSpinsCount\":0,\"totalFreeSpinsCount\":0,\"freeSpinsWin\":0,\"initialFreeSpinWin\":0}}},\"freeSpinsSet4\":{\"multiplier\":1,\"behaviorsState\":{\"freeGamesSlotSceneBehavior\":{\"freeSpinsCount\":0,\"initialFreeSpinsCount\":0,\"totalFreeSpinsCount\":0,\"freeSpinsWin\":0,\"initialFreeSpinWin\":0}}},\"freeSpinsSet6\":{\"multiplier\":1,\"behaviorsState\":{\"freeGamesSlotSceneBehavior\":{\"freeSpinsCount\":0,\"initialFreeSpinsCount\":0,\"totalFreeSpinsCount\":0,\"freeSpinsWin\":0,\"initialFreeSpinWin\":0}}},\"freeSpinsSet3\":{\"multiplier\":1,\"behaviorsState\":{\"freeGamesSlotSceneBehavior\":{\"freeSpinsCount\":0,\"initialFreeSpinsCount\":0,\"totalFreeSpinsCount\":0,\"freeSpinsWin\":0,\"initialFreeSpinWin\":0}}},\"freeSpinsSet5\":{\"multiplier\":1,\"behaviorsState\":{\"freeGamesSlotSceneBehavior\":{\"freeSpinsCount\":0,\"initialFreeSpinsCount\":0,\"totalFreeSpinsCount\":0,\"freeSpinsWin\":0,\"initialFreeSpinWin\":0}}}},\"stake\":{\"lines\":50,\"bet\":1,\"coin\":1},\"history\":[],\"currentRoundWin\":0,\"nextScene\":\"\",\"previousProcessSceneResult\":{\"request\":\"spin\",\"stake\":{\"lines\":1,\"bet\":1,\"coin\":1},\"totalBet\":50,\"totalWin\":0,\"scene\":\"main\",\"multiplier\":1,\"state\":{\"currentScene\":\"main\",\"multiplier\":1},\"reels\":{\"set\":\"main\",\"positions\":[71,31,47,28,59],\"view\":[[10,2,9,10,13],[3,6,2,1,9],[7,11,7,9,1],[1,9,11,12,6]]},\"rewards\":[],\"events\":[],\"roundEnded\":true,\"version\":\"0.3.0\"}},\"settings\":{\"coins\":[1],\"defaultCoin\":1,\"maxTotalStake\":500,\"stakeAll\":[0.1,0.5,1,2,3,5],\"stakeDef\":1,\"stakeMax\":10,\"stakeMin\":0.1,\"winMax\":3000000,\"currencyMultiplier\":100},\"roundId\":5049855,\"roundEnded\":true,\"requestContext\":{\"request\":\"init\",\"requestId\":0,\"name\":\"Test\",\"gameId\":\"sw_al\",\"deviceId\":\"web\",\"startGameToken\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************.MgyzHtgq_sfZECcZHSuKPWqa30NA-34Yf1d46y3f55U\",\"ip\":\"::ffff:127.0.0.1\"},\"createdAt\":\"2017-09-04T10:25:10.639Z\",\"updatedAt\":\"2017-09-04T10:26:52.457Z\"}";
const obj = JSON.parse(ctxAsString);

suite.add("JSON.stringify", () => {
    JSON.stringify(obj);
}).add("msg.pack", () => {
    msgpack.get().pack(obj);
}).on("cycle", (event) => {
    // tslint:disable-next-line:no-console
    console.log(String(event.target));
}).on("complete", function() {
    // tslint:disable-next-line:no-console
    console.log("Fastest is " + this.filter("fastest").map("name"));
}).run({ "async": true });
