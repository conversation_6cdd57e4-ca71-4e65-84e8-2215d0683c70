import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { SinonStub, stub } from "sinon";
import { BaseGameFlowSpec } from "./gameflow.spec";
import { injectRandomGenerator } from "../../../skywind/services/random";
import { EngineGameFlow } from "../../../skywind/services/gameflow";
import { GameInitRequest } from "@skywind-group/sw-game-core";
import config from "../../../skywind/config";

@suite
class GameflowAchievementsSpec extends BaseGameFlowSpec {
    public stubProcessAchievements: SinonStub;
    public flow: EngineGameFlow<GameInitRequest>;

    public async before() {
        await super.before();
        this.flow = await this.createForInit();
        injectRandomGenerator(this.flow);
        // TODO: This method is ignored because this method is not public.
        // @ts-ignore
        this.stubProcessAchievements = stub(this.flow, "processAchievements");
    }

    public after() {
        this.stubProcessAchievements.restore();
        config.achievements.on = false;
    }

    @test
    public async testAchievementsOff() {
        config.achievements.on = false;

        this.flow.deferredUpdate({
            payment: { win: 10, bet: 100 },
            history: { type: "slot", roundEnded: true, data: { field: "some_history" } }
        });

        await this.flow.commitDeferredUpdate();

        expect(this.stubProcessAchievements.notCalled).to.be.true;
    }

    @test
    public async testAchievementsOn() {
        config.achievements.on = true;

        this.flow.deferredUpdate({
            payment: { win: 10, bet: 100 },
            history: { type: "slot", roundEnded: true, data: { field: "some_history" } }
        });

        await this.flow.commitDeferredUpdate();

        expect(this.stubProcessAchievements.calledOnce).to.be.true;
        expect(this.stubProcessAchievements.args[0][0].item.result).to.be.deep.equal({
            "field": "some_history"
        });
    }
}
