import { expect, should, use } from "chai";
import { JackpotPendingModification } from "../../../skywind/services/context/gamecontext";
import { GameContextID } from "../../../skywind/services/contextIds";
import { PaymentOperation } from "../../../skywind/services/wallet";
import GameRecoveryService from "../../../skywind/services/gamerecovery";
import {
    ForbiddenToForceFinishContextError,
    ForbidToRecoverGameWithJPError,
    ForceFlagIsRequiredForOperationError,
    RevertForBrokenPaymentError,
    RoundNotFoundError
} from "../../../skywind/errors";
import { getGameHistory, getRoundHistory } from "../../helper";
import { getArchiveContextModel } from "../../../skywind/services/offlinestorage/models";
import { testing } from "@skywind-group/sw-utils";
import { BaseGameRecoverySpec } from "./gamerecovery.spec";
import { suite, test } from "mocha-typescript";
import status500 = testing.status500;
import status200 = testing.status200;
import { GameContextPersistencePolicy } from "@skywind-group/sw-game-core";

should();
use(require("chai-as-promised"));

@suite()
class GameRecoveryRevertSpec extends BaseGameRecoverySpec {
    @test("fails to revert game if game context not found")
    public async testFailToRevertIfNoContext() {
        await expect(GameRecoveryService.revert({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "1"
        })).rejectedWith(RoundNotFoundError);
    }

    @test("Fail retry on context without round")
    public async testFailToRevertIfNoRound() {
        this.request.put("http://api:3006//v2/play/payment", status500());

        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "1"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } });

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        await (expect(GameRecoveryService.revert({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0", force: true
        }))).rejectedWith(RevertForBrokenPaymentError);

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0); // we don't archive if we don't attempt to revert
        expect(await this.contextManager.findGameContextById(this.gameID)).is.not.undefined;
    }

    @test("failing to revert game context with long persistence policy")
    public async testRevertingRoundWithLongPersistencePolicy() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));

        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });
        this.context.persistencePolicy = GameContextPersistencePolicy.LONG_TERM;
        await this.context.commitPendingModification();

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(0);

        await expect(GameRecoveryService.revert({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        })).to.be.rejectedWith(ForbiddenToForceFinishContextError);
    }

    @test("round is closed already")
    public async testRoundIsClosedAlready() {
        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "1"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });
        await this.context.commitPendingModification();

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(0);

        await expect(GameRecoveryService.revert({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "1"
        })).rejectedWith(RoundNotFoundError);

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
        await expect(this.contextManager.findGameContextById(this.gameID)).is.not.undefined;
    }

    @test("failed, there is JPN context")
    public async testFaildIFHasJackpot() {
        this.request.put("http://api:3006//v2/play/payment", status500());
        await this.context.updateJackpotPendingModification(undefined, undefined, {} as any);
        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "1"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } },
            {
                jackpotOperation: {
                    type: "win-jackpot"
                }
            } as JackpotPendingModification);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        await (expect(GameRecoveryService.revert({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0", force: true
        }))).rejectedWith(ForbidToRecoverGameWithJPError);

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
        await expect(this.contextManager.findGameContextById(this.gameID)).is.not.undefined;
    }

    @test("reverts totalBet")
    public async testRevertTotalBet() {
        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 111,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });
        await this.context.commitPendingModification();

        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "TRX_ID_1",
        }));
        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(0);

        const result = await GameRecoveryService.revert({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        });

        expect(result).deep.equals({ result: "reverted" });
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment/transactionId");
        expect(this.request.args[1].body.transactionId).deep.equals("TRX_ID_1");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.actions).deep.equals([
            {
                "action": "credit",
                "amount": 111,
                "attribute": "balance",
                "changeType": "win"
            },
            {
                "action": "debit",
                "amount": 20,
                "attribute": "balance",
                "changeType": "bet",
            }
        ]);

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[0].type).equals("revert-game");
        expect(gameEvent[0].roundEnded).equals(true);
        expect(roundHistory.length).equals(1);
        expect(roundHistory[0].recoveryType).equals("revert");
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("revert");
        expect(await this.contextManager.findGameContextById(this.gameID)).is.undefined;
    }

    @test("reverts totalBet - use round history")
    public async test() {
        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 111,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });
        await this.context.commitPendingModification();

        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "TRX_ID_1",
        }));
        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(0);

        const result = await GameRecoveryService.revert({
            round: {
                roundId: "0",
                gameCode: this.gameID.gameCode,
                brandId: this.gameID.brandId,
                playerCode: this.gameID.playerCode,
                device: this.gameID.deviceId,
            } as any
        });

        expect(result).deep.equals({ result: "reverted" });
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment/transactionId");
        expect(this.request.args[1].body.transactionId).deep.equals("TRX_ID_1");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.actions).deep.equals([
            {
                "action": "credit",
                "amount": 111,
                "attribute": "balance",
                "changeType": "win"
            },
            {
                "action": "debit",
                "amount": 20,
                "attribute": "balance",
                "changeType": "bet",
            }
        ]);

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[0].type).equals("revert-game");
        expect(gameEvent[0].roundEnded).equals(true);
        expect(roundHistory.length).equals(1);
        expect(roundHistory[0].recoveryType).equals("revert");
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("revert");
        expect(await this.contextManager.findGameContextById(this.gameID)).is.undefined;
    }

    @test("rejects for pending modification and absence of force flag")
    public async testRejectRevertWithoutForce() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "TRX_ID",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } });

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        await expect(GameRecoveryService.revert({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        })).rejectedWith(ForceFlagIsRequiredForOperationError);
    }
}
