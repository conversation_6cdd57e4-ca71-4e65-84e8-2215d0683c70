import { should, use } from "chai";
import { SinonStub, stub } from "sinon";
import { GameFlowContext } from "../../../skywind/services/context/gamecontext";
import { GameTokenData, Settings } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import { createGameToken, flushAll, syncModels, TEST_MODULE_NAME } from "../../helper";
import config from "../../../skywind/config";
import { GameData } from "../../../skywind/services/auth";
import { GameSession } from "../../../skywind/services/gameSession";
import { getArchiveContextModel } from "../../../skywind/services/offlinestorage/models";
import * as GameService from "../../../skywind/services/game/game";
import { DumyGame } from "../../testGames";
import { ContextManager } from "../../../skywind/services/contextmanager/contextManager";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { init as initCurrencyExchange } from "../../../skywind/services/currencyexchange";
import { setRandomGeneratorFactory } from "../../..";
import { RandomGeneratorFactoryImpl } from "../../testRandomFactory";
import { GameContextPersistencePolicy } from "@skywind-group/sw-game-core";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;

should();
use(require("chai-as-promised"));

export class BaseGameRecoverySpec {
    protected contextManager: ContextManager = getGameFlowContextManager();
    protected settings: Settings = {
        coins: [1],
        defaultCoin: 1,
        maxTotalStake: 500,
        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
        stakeDef: 1,
        stakeMax: 10,
        stakeMin: 0.1,
        winMax: 3000000,
        currencyMultiplier: 100,
    };
    protected gameID = GameContextID.create("GM0001", 1, "PLAYER1", "test");

    protected gameTokenData: GameTokenData = {
        playerCode: this.gameID.playerCode,
        gameCode: this.gameID.playerCode,
        brandId: this.gameID.brandId,
        currency: "USD",
        playmode: "real"
    };

    protected gameData: GameData = {
        gameTokenData: this.gameTokenData,
        limits: this.settings
    };

    protected request: RequestMock;
    protected loadGame: SinonStub;
    protected context: GameFlowContext;
    protected session: GameSession;
    protected prevValue: boolean;

    public async before() {
        await syncModels();
        await flushAll();
        this.request = requestMock(superagent);
        this.prevValue = config.walletThroughAPI;
        config.walletThroughAPI = true;
        this.session = await GameSession.generate(this.gameID, "real");
        this.loadGame = stub(GameService, "load");
        this.gameTokenData.token = await createGameToken(this.gameTokenData);
        this.context = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.session,
            this.gameData,
            TEST_MODULE_NAME);
        await getArchiveContextModel().truncate();
        this.loadGame.resolves({ name: this.gameID.gameCode, game: new DumyGame() });
        await initCurrencyExchange();
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
    }

    public async after() {
        config.walletThroughAPI = this.prevValue;
        this.request.unmock(superagent);
        this.loadGame.restore();
        this.context.persistencePolicy = GameContextPersistencePolicy.NORMAL;
    }
}
