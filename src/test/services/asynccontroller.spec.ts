import { expect, use } from "chai";
import { SinonSpy, SinonStub, spy, stub } from "sinon";
import * as Errors from "../../skywind/errors";
import {
    ConcurrentAccessToGameSession,
    EnvIdChangedError,
    GameContextBrokenIntegration,
    GameError,
    InvalidContextCurrencyError,
    ManagementAPIBrokenIntegration,
    ManagementAPITransientError
} from "../../skywind/errors";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import {
    FunStartGameToken,
    GameEvent,
    GameInitRequest,
    GameInitResponse,
    GamePlayResponse
} from "@skywind-group/sw-game-core";
import * as GameService from "../../skywind/services/game/game";
import { AsyncGameController, createAsyncGameController } from "../../skywind/services/asynccontroller";
import { flushAll, syncModels, TestLoadResult } from "../helper";
import { GameData, getService as getAuthservice, LogoutType, StartGameResult } from "../../skywind/services/auth";
import { GameTokenData, generateStartGameToken } from "../../skywind/services/tokens";
import { AsyncTestGame } from "../testGames";
import config from "../../skywind/config";
import { sleep, testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { PlayMode } from "../../skywind/services/playMode";
import { getJPNServer } from "../../skywind/services/jpn/jpnserver";
import { setRandomGeneratorFactory } from "../../index";
import { RandomGeneratorFactoryImpl } from "../testRandomFactory";
import MerchantGameSessionService from "../../skywind/services/merchantGameSessionService";
import { PaymentDelegate } from "../../skywind/services/paymentDelegate";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import status400 = testing.status400;
import status = testing.status;
import status500 = testing.status500;
import { Balance } from "../../skywind/services/wallet";

use(require("chai-as-promised"));
const defaultAmount = config.funGame.defaultFunGameStartAmount * 10;
const win = 11;
const bet = 22;

describe("Async Game Controller", function() {
    this.timeout(3000);

    describe("play", () => {

        let loadGame: SinonStub;
        const pushService = {
            notifyPlayer: () => Promise.resolve(),
            notifyError: () => Promise.resolve(),
            scheduleInternalEvent: () => undefined
        };
        const pushServiceSpy: SinonSpy = spy(pushService, "notifyPlayer");
        const startGameTokenRequest: GameInitRequest = {
            request: "init",
            requestId: 0,
            name: "test",
            gameId: "test",
            deviceId: "test",
            startGameToken: {
                playerCode: "test_player",
                gameCode: "test",
                brandId: 1,
                currency: "USD"
            },
        };

        let request: RequestMock;

        before(async () => {
            request = requestMock(superagent);
            loadGame = stub(GameService, "load");
            await syncModels();
            setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
        });

        beforeEach(async () => {
            await flushAll();

            loadGame.withArgs("test").resolves(new TestLoadResult(new AsyncTestGame(
                [
                    {
                        request: "init",
                    } as GameInitResponse
                ], [],
                [
                    {
                        type: "async",
                        request: {
                            request: "test",
                            requestId: 1,
                        }
                    } as GameEvent
                ],
                [
                    {
                        bet: bet,
                        win: win,
                    }
                ], [],
                [
                    {
                        request: "test",
                        requestId: 1,
                    } as GamePlayResponse
                ],
                10
            )));
            loadGame.withArgs("sw_ss").resolves(new TestLoadResult(new AsyncTestGame(
                [
                    {
                        request: "init",
                    } as GameInitResponse
                ], [],
                [
                    {
                        type: "async",
                        request: {
                            request: "test",
                            requestId: 1,
                        }
                    } as GameEvent
                ],
                [
                    {
                        bet: bet,
                        win: win,
                    }
                ], [],
                [
                    {
                        request: "test",
                        requestId: 1,
                    } as GamePlayResponse
                ],
                10
            ), { request: "spin", reels: { view: [1, 1, 1] } }));

            const funStartGameToken = startGameTokenRequest.startGameToken as FunStartGameToken;
            const gameToken = await generateStartGameToken({
                gameCode: funStartGameToken.gameCode,
                brandId: funStartGameToken.brandId,
                playerCode: funStartGameToken.playerCode,
                currency: funStartGameToken.currency,
                test: true,
                playmode: "fun"
            });
            request.clearRoutes();
            request.post("http://api:3006//v2/play/fun/startgame", status200({
                gameToken,
                jrsdSettings: { a: 1 },
                jurisdictionCode: "GB",
                playedFromCountry: "GB",
                renderType: 0,
                "brandSettings": undefined,
                "gameSettings": undefined,
                settings: {},
                limits: {
                    "USD": {
                        "maxTotalStake": 2000,
                        "stakeAll": [
                            1,
                            2,
                            3,
                            5
                        ],
                        "stakeDef": 1,
                        "stakeMax": 100,
                        "stakeMin": 1,
                        "winMax": 200
                    },
                    "CNY": {
                        "maxTotalStake": 3000,
                        "stakeAll": [
                            2,
                            3,
                            5,
                            10
                        ],
                        "stakeDef": 2,
                        "stakeMax": 200,
                        "stakeMin": 2,
                        "winMax": 400
                    }
                }
            }));
        });

        after(() => {
            loadGame.restore();
            request.unmock(superagent);
        });

        describe("play - with stub AsyncTestGame.play method", () => {
            let playStub: SinonStub;
            before(async () => {
                playStub = stub(AsyncTestGame.prototype, "play");
            });

            after(() => {
                playStub.restore();
            });

            it("'play' throws a game module error that is an instance of SWError", async () => {
                const controller: AsyncGameController = createAsyncGameController(pushService);

                await controller.init(startGameTokenRequest);
                playStub.rejects(new SWError(400, 1234, "validation error"));

                const playPromise = controller.play({
                    request: "test",
                    requestId: 1,
                });

                await expect(playPromise).to.be.rejectedWith(SWError, "validation error");
            });

            it("'play' throws a game module error - mapped to slot-engine SWError", async () => {
                const controller: AsyncGameController = createAsyncGameController(pushService);

                await controller.init(startGameTokenRequest);

                class GameModuleSWError extends Error {
                    public status: number = 400;
                    public code: number = 0;
                    public message: string;

                    constructor(code: number, message: string) {
                        super(message);
                        this.message = message;
                        this.code = code;
                    }
                }

                playStub.throws(new GameModuleSWError(1234, "GameModuleSWError"));

                const playPromise = controller.play({
                    request: "test",
                    requestId: 1,
                });

                await expect(playPromise).to.be.rejectedWith(SWError, "GameModuleSWError");
            });

            it("'play' throws a generic game module error", async () => {
                const controller: AsyncGameController = createAsyncGameController(pushService);

                await controller.init(startGameTokenRequest);
                playStub.throws(new Error("a generic game module error"));

                const playPromise = controller.play({
                    request: "test",
                    requestId: 1,
                });

                await expect(playPromise).to.be.rejectedWith(GameError, "a generic game module error");
            });

        });

        it("processes game 'init', 'play' and 'state' requests", async () => {
            const controller: AsyncGameController = createAsyncGameController(pushService);

            const response = await controller.init(startGameTokenRequest);

            delete (response.gameSession);
            expect(response).to.deep.equal({
                "balance": {
                    "amount": defaultAmount,
                    "bonus": {
                        "amount": 0,
                    },
                    "currency": "USD",
                    "real": {
                        "amount": defaultAmount,
                    },
                },
                "brandSettings": undefined,
                "gameSettings": undefined,
                "jrsdSettings": { a: 1 },
                "jurisdictionCode": "GB",
                "playedFromCountry": "GB",
                "result": {
                    "request": "init",
                },
                "roundEnded": true,
                renderType: undefined,
                brandInfo: undefined,
            });

            const playResponse = await controller.play({
                request: "test",
                requestId: 1,
            });

            expect(playResponse).to.deep.equal({
                "balance": {
                    "amount": defaultAmount + win - bet,
                    "bonus": {
                        "amount": 0,
                    },
                    "currency": "USD",
                    "real": {
                        "amount": defaultAmount + win - bet,
                    },
                },
                "gameSession": undefined,
                "requestId": 1,
                "result": {
                    "request": "test",
                    "requestId": 1,
                },
                "extraData": undefined,
                "roundEnded": false
            });
            expect(pushServiceSpy.calledWithMatch({
                type: "async",
                request: {
                    request: "test",
                    requestId: 1,
                }
            }));

            const stateResponse = await controller.getState({
                request: "state",
                requestId: 2,
                gameSession: "SOME_SESSION",
                getBalance: true,
                getTickers: true,
            });

            expect(stateResponse).deep.equals({
                gameSession: "SOME_SESSION",
                requestId: 2,
                balance:
                    {
                        currency: "USD",
                        amount: defaultAmount + win - bet,
                        real: { amount: defaultAmount + win - bet },
                        bonus: { amount: 0 }
                    },
                result: { request: "state" },
                "extraData": undefined,
                roundEnded: false
            });

            await controller.finish(true);
        });

        it("send logout on disconnect", async () => {
            request.clearRoutes();

            request.get("http://api:3006//v2/play/balance", status200({
                "currency": "USD",
                "main": 1000,
            }));

            const startGameTokenData = {
                playerCode: "test_logout_1",
                gameCode: "test",
                brandId: 1,
                currency: "USD",
                playmode: "real",
                providerGameCode: "test"
            } as any;
            const startGameToken = await generateStartGameToken(startGameTokenData);

            const startGameRequest = {
                request: "init",
                requestId: 0,
                name: "test",
                gameId: "test",
                deviceId: "test_logout",
                startGameToken: startGameToken
            };

            request.post("http://api:3006//v2/play/startgame", status200({
                gameToken: startGameToken,
                jrsdSettings: { a: 1 },
                jurisdictionCode: "GB",
                playedFromCountry: "GB",
                "brandSettings": undefined,
                "gameSettings": undefined,
                settings: {},
                limits: {
                    "USD": {
                        "maxTotalStake": 2000,
                        "stakeAll": [
                            1,
                            2,
                            3,
                            5
                        ],
                        "stakeDef": 1,
                        "stakeMax": 100,
                        "stakeMin": 1,
                        "winMax": 200
                    },
                    "CNY": {
                        "maxTotalStake": 3000,
                        "stakeAll": [
                            2,
                            3,
                            5,
                            10
                        ],
                        "stakeDef": 2,
                        "stakeMax": 200,
                        "stakeMin": 2,
                        "winMax": 400
                    }
                },
                logoutOptions: {
                    maxRetryAttempts: 2,
                    type: LogoutType.ALL,
                    maxSessionTimeout: 1
                },
                renderType: 0,
            }));

            const controller: AsyncGameController = createAsyncGameController(pushService);

            const response = await controller.init(startGameRequest as any);

            delete (response.gameSession);
            expect(response).to.deep.equal({
                "balance": {
                    "amount": 10000,
                    "bonus": {
                        "amount": 0
                    },
                    "currency": "USD",
                    "real": {
                        "amount": 10000
                    }
                },
                "brandSettings": undefined,
                "gameSettings": undefined,
                "jrsdSettings": {
                    "a": 1
                },
                "jurisdictionCode": "GB",
                "playedFromCountry": "GB",
                "result": {
                    "request": "init"
                },
                "roundEnded": true,
                renderType: 0,
                brandInfo: undefined,
            });

            request.post("http://api:3006//v2/play/game/logout", status200({
                requireLogin: false
            }));

            const merchantGameSessionSpy: SinonSpy = spy(MerchantGameSessionService, "logout");

            await controller.finish(true);

            expect(merchantGameSessionSpy.callCount).eq(1);
            expect(merchantGameSessionSpy.args[0][0].gameData.logoutOptions).deep.eq({
                maxRetryAttempts: 2,
                type: LogoutType.ALL,
                maxSessionTimeout: 1
            });

        });

        it("disconnect (from game module) should be executed when game obj is empty", async () => {
            request.clearRoutes();

            request.get("http://api:3006//v2/play/balance", status200({
                "currency": "USD",
                "main": 1000,
            }));

            const startGameTokenData = {
                playerCode: "test_logout_1",
                gameCode: "test",
                brandId: 1,
                currency: "USD",
                playmode: "real",
                providerGameCode: "test"
            } as any;
            const startGameToken = await generateStartGameToken(startGameTokenData);

            const startGameRequest = {
                request: "init",
                requestId: 0,
                name: "test",
                gameId: "test",
                deviceId: "test_logout",
                startGameToken: startGameToken
            };

            request.post("http://api:3006//v2/play/startgame", status200({
                gameToken: startGameToken,
                jrsdSettings: { a: 1 },
                jurisdictionCode: "GB",
                playedFromCountry: "GB",
                "brandSettings": undefined,
                "gameSettings": undefined,
                settings: {},
                limits: {
                    "USD": {
                        "maxTotalStake": 2000,
                        "stakeAll": [
                            1,
                            2,
                            3,
                            5
                        ],
                        "stakeDef": 1,
                        "stakeMax": 100,
                        "stakeMin": 1,
                        "winMax": 200
                    },
                    "CNY": {
                        "maxTotalStake": 3000,
                        "stakeAll": [
                            2,
                            3,
                            5,
                            10
                        ],
                        "stakeDef": 2,
                        "stakeMax": 200,
                        "stakeMin": 2,
                        "winMax": 400
                    }
                },
                logoutOptions: {
                    maxRetryAttempts: 2,
                    type: LogoutType.ALL,
                    maxSessionTimeout: 1
                },
                renderType: 0,
            }));

            const controller: AsyncGameController = createAsyncGameController(pushService);

            await controller.init(startGameRequest as any);

            const gameDisconnectIsExecutedSpy: SinonSpy = stub((controller as any).game, "disconnect");

            (controller as any).game = undefined;
            request.post("http://api:3006//v2/play/game/logout", status200({
                requireLogin: false
            }));

            await controller.finish(true);

            expect(gameDisconnectIsExecutedSpy.callCount).eq(1);
            gameDisconnectIsExecutedSpy.restore();
        });

        it("disconnect - no additional getBalance() request (transferEnabled=false)", async () => {
            request.clearRoutes();

            request.get("http://api:3006//v2/play/balance", status200({
                "currency": "USD",
                "main": 1000,
            }));

            const startGameTokenData = {
                playerCode: "test_logout_1",
                gameCode: "test",
                brandId: 1,
                currency: "USD",
                playmode: "real",
                providerGameCode: "test"
            } as any;
            const startGameToken = await generateStartGameToken(startGameTokenData);

            const startGameRequest = {
                request: "init",
                requestId: 0,
                name: "test",
                gameId: "test",
                deviceId: "test_logout",
                startGameToken: startGameToken
            };

            request.post("http://api:3006//v2/play/startgame", status200({
                gameToken: startGameToken,
                jrsdSettings: { a: 1 },
                jurisdictionCode: "GB",
                playedFromCountry: "GB",
                brandSettings: undefined,
                gameSettings: undefined,
                settings: {},
                limits: {
                    "USD": {
                        "maxTotalStake": 2000,
                        "stakeAll": [1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 100,
                        "stakeMin": 1,
                        "winMax": 200
                    }
                },
                renderType: 0,
            }));

            const controller: AsyncGameController = createAsyncGameController(pushService);
            await controller.init(startGameRequest as any);

            expect(request.args.length).eq(2);
            expect(request.args[0].url).equals("http://api:3006//v2/play/startgame");
            expect(request.args[1].url).equals("http://api:3006//v2/play/balance");

            await controller.finish(true);
            expect(request.args.length).eq(2);
        });

        it("check special state on init in case of logout required", async () => {
            request.clearRoutes();

            request.get("http://api:3006//v2/play/balance", status200({
                "currency": "USD",
                "main": 1000,
            }));

            const startGameTokenData = {
                playerCode: "test_logout_1",
                gameCode: "test",
                brandId: 1,
                currency: "USD",
                playmode: "real",
                providerGameCode: "test"
            } as any;
            const startGameToken = await generateStartGameToken(startGameTokenData);

            const startGameRequest = {
                request: "init",
                requestId: 0,
                name: "test",
                gameId: "test",
                deviceId: "test_logout",
                startGameToken: startGameToken
            };

            request.post("http://api:3006//v2/play/startgame", status200({
                gameToken: startGameToken,
                jrsdSettings: { a: 1 },
                jurisdictionCode: "GB",
                playedFromCountry: "GB",
                "brandSettings": undefined,
                "gameSettings": undefined,
                logoutOptions: {
                    maxRetryAttempts: 2,
                    type: LogoutType.ALL,
                    maxSessionTimeout: 1
                },
                settings: {},
                limits: {
                    "USD": {
                        "maxTotalStake": 2000,
                        "stakeAll": [
                            1,
                            2,
                            3,
                            5
                        ],
                        "stakeDef": 1,
                        "stakeMax": 100,
                        "stakeMin": 1,
                        "winMax": 200
                    },
                    "CNY": {
                        "maxTotalStake": 3000,
                        "stakeAll": [
                            2,
                            3,
                            5,
                            10
                        ],
                        "stakeDef": 2,
                        "stakeMax": 200,
                        "stakeMin": 2,
                        "winMax": 400
                    }
                },
                renderType: 0,
            }));
            request.post("http://api:3006//v2/play/payment/transactionId",
                status200({
                    transactionId: "TRX_ID_1",
                }));

            request.put("http://api:3006//v2/play/payment", status(501, {
                code: 301
            }));

            const controller: AsyncGameController = createAsyncGameController(pushService);

            await controller.init(startGameRequest as any);
            await expect(controller.play({ request: "test", requestId: 1 }))
                .to.be.rejectedWith(ManagementAPIBrokenIntegration);
            await expect(controller.init(startGameRequest as any)).to.be.rejectedWith(GameContextBrokenIntegration);
        });

        it("refresh game-context on disconnect handling - context was modified", async () => {
            request.clearRoutes();

            request.get("http://api:3006//v2/play/balance", status200({
                "currency": "USD",
                "main": 1000,
            }));

            const startGameTokenData = {
                playerCode: "test_logout_1",
                gameCode: "test",
                brandId: 1,
                currency: "USD",
                playmode: "real",
                providerGameCode: "test"
            } as any;
            const startGameToken = await generateStartGameToken(startGameTokenData);

            const startGameRequest = {
                request: "init",
                requestId: 0,
                name: "test",
                gameId: "test",
                deviceId: "test_logout",
                startGameToken: startGameToken
            };

            request.post("http://api:3006//v2/play/startgame", status200({
                gameToken: startGameToken,
                jrsdSettings: { a: 1 },
                jurisdictionCode: "GB",
                playedFromCountry: "GB",
                "brandSettings": undefined,
                "gameSettings": undefined,
                settings: {},
                limits: {
                    "USD": {
                        "maxTotalStake": 2000,
                        "stakeAll": [
                            1,
                            2,
                            3,
                            5
                        ],
                        "stakeDef": 1,
                        "stakeMax": 100,
                        "stakeMin": 1,
                        "winMax": 200
                    },
                    "CNY": {
                        "maxTotalStake": 3000,
                        "stakeAll": [
                            2,
                            3,
                            5,
                            10
                        ],
                        "stakeDef": 2,
                        "stakeMax": 200,
                        "stakeMin": 2,
                        "winMax": 400
                    }
                },
                renderType: 0,
            }));
            request.post("http://api:3006//v2/play/payment/transactionId",
                status200({
                    transactionId: "TRX_ID_1",
                }));

            request.put("http://api:3006//v2/play/payment", status500({
                code: 1
            }));

            const controller: AsyncGameController = createAsyncGameController(pushService);
            await controller.init(startGameRequest as any);
            await expect(controller.play({
                request: "test",
                requestId: 1,
            })).to.be.rejectedWith(ManagementAPITransientError);

            // concurrent modification:
            request.put("http://api:3006//v2/play/payment", status200({
                "currency": "USD",
                "main": 1000,
            }));
            const controller2: AsyncGameController = createAsyncGameController(pushService);
            await controller2.init(startGameRequest as any);

            request.put("http://api:3006//v2/play/payment", status500({
                code: 1
            }));
            /*
            no errors means that broken payment was retried by concurrent modification
            and we do not retry again of disconnect
            */
            await controller.finish(true);
        });

        it("refresh game-context on disconnect handling - context wasn't modified", async () => {
            request.clearRoutes();

            request.get("http://api:3006//v2/play/balance", status200({
                "currency": "USD",
                "main": 1000,
            }));

            const startGameTokenData = {
                playerCode: "test_logout_1",
                gameCode: "test",
                brandId: 1,
                currency: "USD",
                playmode: "real",
                providerGameCode: "test"
            } as any;
            const startGameToken = await generateStartGameToken(startGameTokenData);

            const startGameRequest = {
                request: "init",
                requestId: 0,
                name: "test",
                gameId: "test",
                deviceId: "test_logout",
                startGameToken: startGameToken
            };

            request.post("http://api:3006//v2/play/startgame", status200({
                gameToken: startGameToken,
                jrsdSettings: { a: 1 },
                jurisdictionCode: "GB",
                playedFromCountry: "GB",
                "brandSettings": undefined,
                "gameSettings": undefined,
                settings: {},
                limits: {
                    "USD": {
                        "maxTotalStake": 2000,
                        "stakeAll": [
                            1,
                            2,
                            3,
                            5
                        ],
                        "stakeDef": 1,
                        "stakeMax": 100,
                        "stakeMin": 1,
                        "winMax": 200
                    },
                    "CNY": {
                        "maxTotalStake": 3000,
                        "stakeAll": [
                            2,
                            3,
                            5,
                            10
                        ],
                        "stakeDef": 2,
                        "stakeMax": 200,
                        "stakeMin": 2,
                        "winMax": 400
                    }
                },
                renderType: 0,
            }));
            request.post("http://api:3006//v2/play/payment/transactionId",
                status200({
                    transactionId: "TRX_ID_1",
                }));

            request.put("http://api:3006//v2/play/payment", status500({
                code: 1
            }));

            const controller: AsyncGameController = createAsyncGameController(pushService);
            await controller.init(startGameRequest as any);
            await expect(controller.play({
                request: "test",
                requestId: 1,
            })).to.be.rejectedWith(ManagementAPITransientError);
            // error expected on retry attempt
            await expect(controller.finish(true)).to.be.rejectedWith(ManagementAPITransientError);
        });

        it("processes game 'init' for legacy game, no win result should be added", async () => {
            const controller: AsyncGameController = createAsyncGameController(pushService);

            const customRequest = { ...startGameTokenRequest, gameId: "sw_ss" };
            const response: any = await controller.init(customRequest as any);

            expect(response.result.defaultResult).not.equal(undefined);
            expect(response.result.defaultResult.reels).not.equal(undefined);
        });

        it("processes concurrent game sessions", async () => {

            const controller1: AsyncGameController = createAsyncGameController(pushService);
            await controller1.init(startGameTokenRequest);

            const controller2: AsyncGameController = createAsyncGameController(pushService);
            await controller2.init(startGameTokenRequest);

            await expect(controller1.play({
                request: "test",
                requestId: 1,
            })).to.be.rejectedWith(ConcurrentAccessToGameSession);

            await controller1.finish(false);
            await controller2.finish(true);
        });

        it("changing player currency in runtime must throw error", async () => {

            const controller: AsyncGameController = createAsyncGameController(pushService);

            await controller.init(startGameTokenRequest);

            const funStartGameToken = startGameTokenRequest.startGameToken as FunStartGameToken;
            const gameToken = await generateStartGameToken({
                gameCode: funStartGameToken.gameCode,
                brandId: funStartGameToken.brandId,
                playerCode: funStartGameToken.playerCode,
                currency: "EUR",
                playmode: "fun"
            });
            request.post("http://api:3006//v2/play/fun/startgame", status200({
                gameToken,
                jrsdSettings: { a: 1 },
                settings: {},
                limits: {
                    "EUR": {
                        "maxTotalStake": 2000,
                        "stakeAll": [1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 100,
                        "stakeMin": 1,
                        "winMax": 200
                    }
                }
            }));
            const startGameTokenRequest1: GameInitRequest = {
                request: "init",
                requestId: 0,
                name: "test",
                gameId: "test",
                deviceId: "test",
                startGameToken: {
                    playerCode: "test_player",
                    gameCode: "test",
                    brandId: 1,
                    currency: "EUR"
                } as FunStartGameToken,
            };

            await expect(controller.init(startGameTokenRequest1)).to.be.rejectedWith(InvalidContextCurrencyError);

            await controller.finish(false);
        });

        it("commit pending concurrently - no second commit on false check pending flag", async () => {
            const controller: AsyncGameController = createAsyncGameController(pushService);
            // TODO: This method is ignored because this method is not public.
            // @ts-ignore
            const paymentDelegateMock = stub<Balance>(PaymentDelegate.prototype, "commitWalletOperation");
            await controller.init(startGameTokenRequest);

            paymentDelegateMock.callsFake(async () => {
                await sleep(500);
                return { main: 100 };
            });

            const playPromise = controller.play({
                request: "test",
                requestId: 1,
            });
            await sleep(100);
            const finishPromise = controller.finish(false);
            await Promise.all([playPromise, finishPromise]);
            expect(paymentDelegateMock.callCount).to.equal(1);
            paymentDelegateMock.restore();
        });

        describe("JPN error handling", () => {
            let supportJP: SinonStub;
            beforeEach(async () => {
                supportJP = stub(PlayMode, "supportJP");
                supportJP.returns(true);
                const gameToken = await generateStartGameToken({
                    playerCode: "test_player",
                    gameCode: "test",
                    brandId: 1,
                    currency: "USD",
                    test: true,
                    playmode: "fun"
                });
                request.clearRoutes();
                request.post("http://api:3006//v2/play/fun/startgame", status200({
                    gameToken,
                    jrsdSettings: { a: 1 },
                    playedFromCountry: "GB",
                    settings: {
                        jackpotId: "jp_id"
                    },
                    limits: {
                        "CNY": {
                            "maxTotalStake": 3000,
                            "stakeAll": [
                                2,
                                3,
                                5,
                                10
                            ],
                            "stakeDef": 2,
                            "stakeMax": 200,
                            "stakeMin": 2,
                            "winMax": 400
                        }
                    },
                    jackpots: [
                        {
                            id: "jp-id",
                            isGameOwned: true,
                            gameHistoryEnabled: true,
                            paymentStatisticEnabled: true
                        }
                    ]
                }));
            });
            it("wrong jackpot configuration should throw error", async () => {
                const controller: AsyncGameController = createAsyncGameController(pushService);
                const jpnInstance = getJPNServer();
                const authStub = stub(jpnInstance, "auth");
                authStub.throws(new Errors.JPNBadRequestError(404, { code: 24 } as SWError));
                await expect(controller.init(startGameTokenRequest)).to.be.rejectedWith(Errors.JPNBadRequestError);
                authStub.restore();
                await controller.finish(false);
            });
            it("jpn 400 errors except wrong configuration should be ignored", async () => {
                const controller: AsyncGameController = createAsyncGameController(pushService);
                const jpnInstance = getJPNServer();
                const authStub = stub(jpnInstance, "auth");
                authStub.throws(new Errors.JPNBadRequestError(404, { code: 25 } as SWError));
                await expect(controller.init(startGameTokenRequest)).to.be.ok;
                authStub.restore();
                await controller.finish(true);
            });
            it("jpn 500 errors should be thrown", async () => {
                const controller: AsyncGameController = createAsyncGameController(pushService);
                const jpnInstance = getJPNServer();
                const authStub = stub(jpnInstance, "auth");
                authStub.throws(new Errors.JPNInternalServerError(500, { code: 25 } as SWError));
                await expect(controller.init(startGameTokenRequest)).to.be.rejectedWith(Errors.JPNInternalServerError);
                authStub.restore();
                await controller.finish(true);
            });
            afterEach(async () => {
                supportJP.restore();
            });
        });

        it("tryToDisconnect do not throw when this.game is undefined", async () => {
                const controller: AsyncGameController = await createAsyncGameController(pushService);
                delete (controller as any).game;
                await (controller as any).tryToDisconnect();
        });

    });

    it("new controller per connection", () => {
        const controller1 = createAsyncGameController(null);
        const controller2 = createAsyncGameController(null);
        expect(controller1).not.equal(controller2);
    });

    describe("uses keep alive timer", () => {

        const pushService = {
            notifyPlayer: () => Promise.resolve(),
            notifyError: () => Promise.resolve(),
            scheduleInternalEvent: () => undefined
        };
        let notifyError: SinonStub;
        const controller: AsyncGameController = createAsyncGameController(pushService);
        let loadGame: SinonStub;
        let startGame: SinonStub;
        const gameTokenData: GameTokenData = {
            playerCode: "test_player",
            gameCode: "test",
            brandId: 1,
            currency: "USD",
            playmode: "real"
        };
        const tokenData: GameData = {
            gameTokenData: gameTokenData,
            settings: {
                transferEnabled: false,
                keepAliveSec: 1,
            },
            limits: {},
            jrsdSettings: { a: 1 },
        };

        const startGameResult = {
            gameData: tokenData,
            clientSettings: {}
        } as StartGameResult;

        const asyncTestGame = new AsyncTestGame([{ request: "init" } as GameInitResponse], [], [], [], [], [], 10);

        let disconnectStub: SinonStub;
        let prevValue;
        let request: RequestMock;

        before(async () => {
            request = requestMock(superagent);
            prevValue = config.retries.sleep;
            // disable retries
            config.retries.sleep = 0;
            await flushAll();
            notifyError = stub(pushService, "notifyError");
            notifyError.returns(undefined);

            gameTokenData.token = await generateStartGameToken(gameTokenData);
            startGame = stub(getAuthservice("fun"), "startGame");
            startGame.returns(startGameResult);

            loadGame = stub(GameService, "load");

            disconnectStub = stub(asyncTestGame, "disconnect");
            loadGame.returns(Promise.resolve(new TestLoadResult(asyncTestGame)));
            request.clearRoutes();
        });

        after(() => {
            request.unmock(superagent);
            notifyError.restore();
            loadGame.restore();
            startGame.restore();
            disconnectStub.restore();
            config.environmentId = undefined;
            config.retries.sleep = prevValue;
        });

        afterEach(() => {
            disconnectStub.reset();
            request.clearRoutes();
        });

        it("keeps alive", async () => {
            await controller.init({
                request: "init",
                requestId: 0,
                name: "test",
                gameId: "test",
                deviceId: "test",
                startGameToken: gameTokenData,
            });
            request.get("http://api:3006//v1/play/balance", status200());
            await sleep(1500);

            await controller.finish(false);
            expect(disconnectStub.callCount).eq(1);
            expect(request.args).is.not.undefined;
        });

        it("stops keep alive on error", async () => {
            request.get("http://api:3006//v1/play/balance", status400({
                errorCode: 1000
            }));

            await controller.init({
                request: "init",
                requestId: 0,
                name: "test",
                gameId: "test",
                deviceId: "test",
                startGameToken: gameTokenData,
            });

            await sleep(1500);

            await controller.finish(false);
            expect(disconnectStub.callCount).eq(1);

            expect(request.args).is.not.undefined;
            expect(notifyError.called).to.be.true;
        });

        it("should fail with envId error", async () => {
            request.get("http://api:3006/v1//play/balance", status200());
            const myGameTokenData: any = {
                playerCode: "test_player",
                gameCode: "test",
                brandId: 1,
                currency: "USD",
                envId: "someEnvId"
            };
            config.environmentId = "slot-eng-1";
            await expect(controller.init({
                request: "init",
                requestId: 0,
                name: "test",
                gameId: "test",
                deviceId: "test",
                startGameToken: myGameTokenData,
            })).to.be.rejectedWith(EnvIdChangedError);
            config.environmentId = undefined;
            expect(request.args).is.undefined;
        });

        it("should not fail with envId error if envId is not in token", async () => {
            request.get("http://api:3006//v1/play/balance", status200());
            const myGameTokenData: any = {
                playerCode: "test_player",
                gameCode: "test",
                brandId: 1,
                currency: "USD"
            };
            config.environmentId = "slot-eng-1";
            await expect(controller.init({
                request: "init",
                requestId: 0,
                name: "test",
                gameId: "test",
                deviceId: "test",
                startGameToken: myGameTokenData,
            })).to.be.not.rejectedWith(EnvIdChangedError);
            config.environmentId = undefined;
            expect(request.args).is.undefined;
        });
    });
});
