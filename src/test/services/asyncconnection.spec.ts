import { suite, test, timeout } from "mocha-typescript";
import { AsyncGameConnection } from "../../skywind/services/asyncconnection";
import { SinonStub, stub } from "sinon";
import {
    FunStartGameToken,
    GameEvent,
    GameInitRequest,
    GameInitResponse,
    GamePlayResponse,
} from "@skywind-group/sw-game-core";
import * as superagent from "superagent";
import * as GameService from "../../skywind/services/game/game";
import { flushAll, syncModels, TestLoadResult } from "../helper";
import { setRandomGeneratorFactory } from "../..";
import { RandomGeneratorFactoryImpl } from "../testRandomFactory";
import { sleep, testing } from "@skywind-group/sw-utils";
import { AsyncTestGameWithAwaitForDisconnect } from "../testGames";
import { generateStartGameToken } from "../../skywind/services/tokens";
import { expect } from "chai";
import config from "../../skywind/config";
import * as Errors from "../../skywind/errors";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import { PaymentDelegate } from "../../skywind/services/paymentDelegate";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { Balance } from "../../skywind/services/wallet";

const defaultAmount = config.funGame.defaultFunGameStartAmount * 10;
const win = 11;
const bet = 22;

@suite()
class AsyncConnectionSpec {
    private loadGame: SinonStub;
    private startGameTokenRequest: GameInitRequest = {
        request: "init",
        requestId: 0,
        name: "test",
        gameId: "test",
        deviceId: "test",
        startGameToken: {
            playerCode: "test_player",
            gameCode: "test",
            brandId: 1,
            currency: "USD"
        },
    };

    private request: RequestMock;

    public async before() {
        this.request = requestMock(superagent);
        this.loadGame = stub(GameService, "load");
        await syncModels();
        await flushAll();
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());

        this.loadGame.withArgs("test")
            .returns(Promise.resolve(new TestLoadResult(new AsyncTestGameWithAwaitForDisconnect(
                [
                    {
                        request: "init",
                    } as GameInitResponse
                ], [],
                [
                    {
                        type: "async",
                        request: {
                            request: "test",
                            requestId: 1,
                        }
                    } as GameEvent
                ],
                [
                    {
                        bet,
                        win,
                    }
                ], [],
                [
                    {
                        request: "test1",
                        requestId: 1,
                    } as GamePlayResponse,
                    {
                        request: "request2",
                        requestId: 2,
                    } as GamePlayResponse
                ],
                10
            ))));

        const funStartGameToken = this.startGameTokenRequest.startGameToken as FunStartGameToken;
        const gameToken = await generateStartGameToken({
            gameCode: funStartGameToken.gameCode,
            brandId: funStartGameToken.brandId,
            playerCode: funStartGameToken.playerCode,
            currency: funStartGameToken.currency,
            test: true,
            playmode: "real"
        });
        this.request.clearRoutes();
        this.request.post("http://api:3006//v2/play/startgame", status200({
            gameToken,
            jrsdSettings: { a: 1 },
            jurisdictionCode: "GB",
            playedFromCountry: "GB",
            "brandSettings": undefined,
            "gameSettings": undefined,
            settings: {},
            limits: {
                "USD": {
                    "maxTotalStake": 2000,
                    "stakeAll": [
                        1,
                        2,
                        3,
                        5
                    ],
                    "stakeDef": 1,
                    "stakeMax": 100,
                    "stakeMin": 1,
                    "winMax": 200
                },
                "CNY": {
                    "maxTotalStake": 3000,
                    "stakeAll": [
                        2,
                        3,
                        5,
                        10
                    ],
                    "stakeDef": 2,
                    "stakeMax": 200,
                    "stakeMin": 2,
                    "winMax": 400
                }
            }
        }));
    }

    public async after() {
        this.request.unmock(superagent);
        this.loadGame.restore();
    }

    @test()
    @timeout(6000)
    public async processInternalEventAfterDisconnect() {
        const asyncGameClient: any = {
            sendError: stub(),
            sendEvent: stub(),
            sendResponse: stub(),
            disconnect: stub()
        };
        const connection = new AsyncGameConnection(asyncGameClient);
        await connection.init("", this.startGameTokenRequest);

        connection.request("", {
            request: "test",
            requestId: 1,
        });
        connection.disconnect();

        await sleep(4000);
        const response = asyncGameClient.sendResponse.args[0][1];
        delete (response.gameSession);

        expect(response).to.deep.equal({
            "balance": {
                "amount": defaultAmount,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": defaultAmount,
                },
            },
            "brandSettings": undefined,
            "gameSettings": undefined,
            "jrsdSettings": undefined,
            "jurisdictionCode": undefined,
            "playedFromCountry": undefined,
            "renderType": undefined,
            "result": {
                "request": "init",
            },
            "roundEnded": true,
            "brandInfo": undefined
        });
        const playResponse1 = asyncGameClient.sendResponse.args[1][1];
        expect(playResponse1).to.deep.equal({
            "balance": {
                "amount": defaultAmount + win - bet,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": defaultAmount + win - bet,
                },
            },
            "gameSession": undefined,
            "requestId": 1,
            "result": {
                "request": "test1",
                "requestId": 1,
            },
            "extraData": undefined,
            "roundEnded": false
        });

        const playResponse2 = asyncGameClient.sendResponse.args[2][1];
        expect(playResponse2).to.deep.equal({
            "balance": {
                "amount": defaultAmount + win - bet,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": defaultAmount + win - bet,
                },
            },
            "gameSession": undefined,
            "requestId": undefined,
            "result": {
                "request": "request2",
                "requestId": 2,
            },
            "extraData": undefined,
            "roundEnded": false,
            "roundTotalBet": 2.2,
            "roundTotalWin": 1.1
        });

    }

    @test()
    @timeout(6000)
    public async requestAndNotifyErrorConcurrency() {
        // TODO: This method is ignored because this method is not public.
        // @ts-ignore
        const paymentDelegateMock = stub<Balance>(PaymentDelegate.prototype, "commitWalletOperation");
        paymentDelegateMock.callsFake(async () => {
            await sleep(500);
            return { main: 100 };
        });
        const asyncGameClient: any = {
            resolve: undefined,
            sendError: stub(),
            sendEvent: stub(),
            disconnect: stub(),
            sendResponse() {
                this.resolve();
            }
        };
        const connection = new AsyncGameConnection(asyncGameClient);
        await connection.init("", this.startGameTokenRequest);
        const initPromise = new Promise(resolve => {
            asyncGameClient.resolve = resolve;
        });
        await initPromise;

        const game: AsyncTestGameWithAwaitForDisconnect = (connection as any).processor.controller.game.game;
        const playPromise = new Promise(resolve => {
            asyncGameClient.resolve = resolve;
        });
        await connection.request("", {
            request: "test",
            requestId: 1,
        });
        await sleep(1000);
        // During play request with payment game sends to pushService error
        const errorPromise = game.notifyError(new Error());
        await Promise.all([playPromise, errorPromise]);
        expect(paymentDelegateMock.callCount).to.equal(1);
        paymentDelegateMock.restore();
    }

    @test()
    @timeout(3000)
    public async disconnectOnCannotCompletePayment() {
        const asyncGameClient: any = {
            sendError: stub(),
            sendResponse: stub(),
        };
        const connection = new AsyncGameConnection(asyncGameClient);
        await connection.init("", this.startGameTokenRequest);
        const processor = (connection as any).processor;
        const processRequest: SinonStub = stub(processor, "processRequest");
        // TODO: This method is ignored because this method is not public.
        // @ts-ignore
        const processDisconnect: SinonStub = stub(connection, "processDisconnect");
        processRequest.onFirstCall().callsFake(async () => {
            throw new SWError(500, 806, "CannotCompletePayment");
        });
        connection.request("getBalance", {
            request: "test",
            requestId: 1,
        });
        await sleep(2000);
        expect(processDisconnect.callCount).eq(1);
    }

    // Local usage only - too long test
    @test.skip()
    @timeout(11000)
    public async processDisconnectOnErrorWhenRequestQueueIsLong() {
        let errorsCount = 0;
        const asyncGameClient: any = {
            sendError: () => {
                ++errorsCount;
            },
            sendEvent: stub(),
            sendResponse: stub(),
            disconnect: stub()
        };
        const connection = new AsyncGameConnection(asyncGameClient);
        await connection.init("", this.startGameTokenRequest);
        const processor = (connection as any).processor;
        const processRequest: SinonStub = stub(processor, "processRequest");
        processRequest.onFirstCall().callsFake(async () => {
            await sleep(100);
            throw new Errors.ConcurrentAccessToGameSession();
        });
        for (let i = 1; i < 100; i++) {
            processRequest.onCall(i).throws(new Errors.ConcurrentAccessToGameSession());
        }
        for (let i = 0; i < 100; i++) {
            connection.request("getBalance", {
                request: "test",
                requestId: 1,
            });
        }
        await sleep(10000);
        expect(errorsCount).lessThan(100);
    }

    @test()
    @timeout(6000)
    public async skipConsecutiveBalanceRequests() {
        const asyncGameClient: any = {
            sendError: stub(),
            sendEvent: stub(),
            sendResponse: stub(),
            disconnect: stub()
        };
        const connection = new AsyncGameConnection(asyncGameClient);
        await connection.init("", this.startGameTokenRequest);
        const processor = (connection as any).processor;
        const processRequest: SinonStub = stub(processor, "processRequest");
        let requestCounter = 0;
        processRequest.callsFake(async () => {
            await sleep(100);
            requestCounter++;
            return { main: 1000 };
        });
        for (let i = 0; i < 100; i++) {
            const request = (i % 10 === 0) ? "test" : "balance";
            connection.request("getBalance", {
                request,
                requestId: 1
            });
        }
        await sleep(5000);
        expect(requestCounter).greaterThan(10);
        expect(requestCounter).lessThan(101);
    }

    @test()
    @timeout(4000)
    public async noUnhandledRejectionOnCallbackError() {
        let unhandled = false;
        const unhandledListener = (reason) => {
            unhandled = true;
        };
        process.once("unhandledRejection", unhandledListener);

        const asyncGameClient: any = {
            sendError: stub(),
            sendEvent: stub(),
            sendResponse: stub(),
            disconnect: stub()
        };
        const connection = new AsyncGameConnection(asyncGameClient);
        await connection.init("", this.startGameTokenRequest);

        const processor = (connection as any).processor;
        const asyncRequest = (connection as any).processor.controller && {
            baseRequest: { request: "test", requestId: 1 },
            requestTs: Date.now(),
            continueTransaction: (fn) => Promise.resolve(fn()),
        };

        await processor.process(
            asyncRequest,
            () => Promise.reject(new Error("Test error in callback")),
            false
        );

        await sleep(3000);
        process.removeListener("unhandledRejection", unhandledListener);
        expect(unhandled).to.be.false;
    }
}
