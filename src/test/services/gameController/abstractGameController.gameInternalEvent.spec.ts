import { expect } from "chai";
import * as sinon from "sinon";
import { EngineGameFlow } from "../../../skywind/services/gameflow";
import { GamePlayRequest, GamePlayResponse, LiveGamePlayResponse, BaseRequest } from "@skywind-group/sw-game-core";
import * as randomModule from "../../../skywind/services/random";
import { GameError } from "../../../skywind/errors";
import { SWError, ERROR_LEVEL, ErrorSpecialFlag } from "@skywind-group/sw-wallet-adapter-core";
import { AbstractGameController } from "../../../skywind/services/gamecontroller";

// Mock implementation of AbstractGameController for testing
class TestGameController extends AbstractGameController {
    protected async createFlowForRequest<T extends BaseRequest>(
        req: T,
        checkConcurrency?: boolean,
        checkPending?: boolean
    ): Promise<EngineGameFlow<T>> {
        // Return a mock flow for testing
        const mockFlow = {
            game: {
                internalEvent: sinon.stub()
            }
        } as any;
        return mockFlow;
    }

    // Expose private method for testing
    public testWrapGameError(err: any): Promise<any> {
        return (this as any).wrapGameError(err);
    }

    // Expose protected method for testing
    public testGameInternalEvent(
        flow: EngineGameFlow<GamePlayRequest>
    ): Promise<GamePlayResponse | LiveGamePlayResponse> {
        return this.gameInternalEvent(flow);
    }
}

describe("AbstractGameController.gameInternalEvent", () => {
    let controller: TestGameController;
    let mockFlow: any;
    let mockGame: any;
    let injectRandomGeneratorStub: sinon.SinonStub;

    before(() => {
        injectRandomGeneratorStub = sinon.stub(randomModule, "injectRandomGenerator");
    });

    after(() => {
        sinon.restore();
    });

    beforeEach(() => {
        controller = new TestGameController();

        // Create mock game with stubbed methods
        mockGame = {
            internalEvent: sinon.stub()
        };

        // Create mock flow
        mockFlow = {
            game: mockGame
        };
    });

    afterEach(() => {
        injectRandomGeneratorStub.resetHistory();
        mockGame?.internalEvent?.reset();
    });

    describe("successful execution", () => {
        it("should inject random generator and call game.internalEvent", async () => {
            const expectedResponse: GamePlayResponse = { request: "play", totalBet: 10, totalWin: 20 };
            mockGame.internalEvent.resolves(expectedResponse);

            const result = await controller.testGameInternalEvent(mockFlow);

            expect(injectRandomGeneratorStub.calledOnce).to.be.true;
            expect(injectRandomGeneratorStub.calledWith(mockFlow)).to.be.true;
            expect(mockGame.internalEvent.calledOnce).to.be.true;
            expect(mockGame.internalEvent.calledWith(mockFlow)).to.be.true;
            expect(result).to.equal(expectedResponse);
        });

        it("should handle LiveGamePlayResponse", async () => {
            const expectedResponse: LiveGamePlayResponse = {
                response: { request: "play", totalBet: 5, totalWin: 15 },
                dontUseBalance: true
            };
            mockGame.internalEvent.resolves(expectedResponse);

            const result = await controller.testGameInternalEvent(mockFlow);

            expect(result).to.equal(expectedResponse);
        });

        it("should handle regular GamePlayResponse", async () => {
            const expectedResponse: GamePlayResponse = {
                request: "play",
                totalBet: 100,
                totalWin: 0
            };
            mockGame.internalEvent.resolves(expectedResponse);

            const result = await controller.testGameInternalEvent(mockFlow);

            expect(result).to.equal(expectedResponse);
            expect(injectRandomGeneratorStub.calledWith(mockFlow)).to.be.true;
        });
    });

    describe("error handling", () => {
        it("should catch and wrap game errors properly", async () => {
            const gameError = new Error("Game internal error");
            mockGame.internalEvent.rejects(gameError);

            try {
                await controller.testGameInternalEvent(mockFlow);
                expect.fail("Should have thrown error");
            } catch (error) {
                expect(error).to.be.instanceof(GameError);
                expect(error.message).to.contain("Game internal error");
            }
        });

        it("should handle SWError instances correctly", async () => {
            const swError = new SWError(500, 1, "SW Error", ERROR_LEVEL.ERROR);
            swError.setSpecialFlag = sinon.stub().returns(swError);
            mockGame.internalEvent.rejects(swError);

            try {
                await controller.testGameInternalEvent(mockFlow);
                expect.fail("Should have thrown error");
            } catch (error) {
                expect(error).to.equal(swError);
                expect(
                    (swError.setSpecialFlag as sinon.SinonStub).calledWith(ErrorSpecialFlag.GAME_MODULE_ERROR)
                ).to.be.true;
            }
        });

        it("should properly handle promise rejections in catch chain", async () => {
            const gameError = new Error("Promise chain error");
            mockGame.internalEvent.rejects(gameError);

            let caughtError: any;
            try {
                await controller.testGameInternalEvent(mockFlow);
            } catch (error) {
                caughtError = error;
            }

            expect(caughtError).to.exist;
            expect(caughtError).to.be.instanceof(GameError);
        });
    });

    describe("edge cases", () => {
        it("should handle null/undefined flow", async () => {
            try {
                await controller.testGameInternalEvent(null as any);
                expect.fail("Should have thrown error");
            } catch (error) {
                expect(error).to.be.instanceof(TypeError);
            }
        });

        it("should handle flow with null/undefined game", async () => {
            const flowWithoutGame = { game: null } as any;

            try {
                await controller.testGameInternalEvent(flowWithoutGame);
                expect.fail("Should have thrown error");
            } catch (error) {
                expect(error).to.be.instanceof(GameError);
            }
        });

        it("should handle game without internalEvent method", async () => {
            const gameWithoutMethod = {} as any;
            const flowWithInvalidGame: any = { game: gameWithoutMethod };

            try {
                await controller.testGameInternalEvent(flowWithInvalidGame);
                expect.fail("Should have thrown error");
            } catch (error) {
                expect(error).to.be.instanceof(GameError);
                expect(error.message).to.contain("internalEvent is not a function");
            }
        });

        it("should handle undefined game.internalEvent", async () => {
            const gameWithUndefinedMethod = { internalEvent: undefined } as any;
            const flowWithUndefinedMethod: any = { game: gameWithUndefinedMethod };

            try {
                await controller.testGameInternalEvent(flowWithUndefinedMethod);
                expect.fail("Should have thrown error");
            } catch (error) {
                expect(error).to.be.instanceof(GameError);
            }
        });
    });

    describe("wrapGameError functionality", () => {
        it("should wrap generic errors as GameError", async () => {
            const genericError = new Error("Generic error");

            try {
                await controller.testWrapGameError(genericError);
                expect.fail("Should have thrown error");
            } catch (error) {
                expect(error).to.be.instanceof(GameError);
                expect(error.message).to.contain("Generic error");
            }
        });

        it("should handle SWError with setSpecialFlag method", async () => {
            const swError = new SWError(400, 1, "Test error", ERROR_LEVEL.ERROR);
            swError.setSpecialFlag = sinon.stub().returns(swError);

            try {
                await controller.testWrapGameError(swError);
                expect.fail("Should have thrown error");
            } catch (error) {
                expect(error).to.equal(swError);
                expect(
                    (swError.setSpecialFlag as sinon.SinonStub).calledWith(ErrorSpecialFlag.GAME_MODULE_ERROR)
                ).to.be.true;
            }
        });

        it("should handle SWError-like objects without setSpecialFlag method", async () => {
            const swErrorLike = {
                responseStatus: 500,
                code: "ERR002",
                message: "Error without setSpecialFlag"
            };

            try {
                await controller.testWrapGameError(swErrorLike);
                expect.fail("Should have thrown error");
            } catch (error) {
                expect(error).to.be.instanceof(SWError);
                expect(error.code).to.equal("ERR002");
                expect(error.message).to.equal("Error without setSpecialFlag");
            }
        });
    });

    describe("unhandled rejection detection", () => {
        let originalListeners: NodeJS.UnhandledRejectionListener[];

        beforeEach(() => {
            originalListeners = process.listeners("unhandledRejection") as NodeJS.UnhandledRejectionListener[];
            process.removeAllListeners("unhandledRejection");
        });

        afterEach(() => {
            process.removeAllListeners("unhandledRejection");
            originalListeners.forEach(listener => {
                process.on("unhandledRejection", listener);
            });
        });

        it("should not create unhandled rejections when error is properly caught", (done) => {
            let unhandledRejectionOccurred = false;

            process.on("unhandledRejection", () => {
                unhandledRejectionOccurred = true;
            });

            const gameError = new Error("Test error for unhandled rejection test");
            mockGame.internalEvent.rejects(gameError);

            controller.testGameInternalEvent(mockFlow)
                .catch(() => {
                    // Error properly caught
                })
                .finally(() => {
                    setTimeout(() => {
                        expect(unhandledRejectionOccurred).to.be.false;
                        done();
                    }, 100);
                });
        });

        it("should test the current implementation promise chain handling", async () => {
            const gameError = new Error("Promise chain test");
            mockGame.internalEvent.rejects(gameError);

            let caughtError: any;
            try {
                await controller.testGameInternalEvent(mockFlow);
            } catch (error) {
                caughtError = error;
            }

            expect(caughtError).to.be.instanceof(GameError);
            expect(caughtError.message).to.contain("Promise chain test");
        });

        it("should demonstrate proper error propagation without unhandled rejections", (done) => {
            const gameError = new Error("Error propagation test");
            mockGame.internalEvent.rejects(gameError);

            let unhandledRejectionOccurred = false;
            let caughtError: any;

            process.on("unhandledRejection", (reason) => {
                unhandledRejectionOccurred = true;
                // tslint:disable-next-line:no-console
                console.log("Unexpected unhandled rejection:", reason);
            });

            controller.testGameInternalEvent(mockFlow)
                .then(() => {
                    expect.fail("Should not resolve");
                })
                .catch((error) => {
                    caughtError = error;
                })
                .finally(() => {
                    setTimeout(() => {
                        expect(unhandledRejectionOccurred).to.be.false;
                        expect(caughtError).to.be.instanceof(GameError);
                        done();
                    }, 100);
                });
        });
    });

    describe("context binding verification", () => {
        it("should verify that catch handler maintains proper context", async () => {
            const gameError = new Error("Context test");
            mockGame.internalEvent.rejects(gameError);

            try {
                await controller.testGameInternalEvent(mockFlow);
                expect.fail("Should have thrown error");
            } catch (error) {
                // The error should be properly wrapped
                expect(error).to.be.instanceof(GameError);
            }
        });

        it("should test potential issues with method reference binding", async () => {
            const gameError = new Error("Method binding test");
            mockGame.internalEvent.rejects(gameError);

            // Test calling the method through different contexts
            const methodRef = controller.testGameInternalEvent;
            const boundMethod = methodRef.bind(controller);

            let error1: any;
            let error2: any;

            // Call with explicit binding
            try {
                await boundMethod(mockFlow);
            } catch (err) {
                error1 = err;
            }

            // Reset the mock for second call
            mockGame.internalEvent.rejects(gameError);

            // Call directly on controller
            try {
                await controller.testGameInternalEvent(mockFlow);
            } catch (err) {
                error2 = err;
            }

            // Both should produce the same type of error
            expect(error1).to.be.instanceof(GameError);
            expect(error2).to.be.instanceof(GameError);
            expect(error1.constructor).to.equal(error2.constructor);
        });

        it("should verify the actual catch mechanism works correctly", async () => {
            const gameError = new Error("Catch mechanism test");
            mockGame.internalEvent.rejects(gameError);

            // Use a spy to verify that injectRandomGenerator is called before the error
            let injectionCalled = false;
            injectRandomGeneratorStub.callsFake(() => {
                injectionCalled = true;
            });

            try {
                await controller.testGameInternalEvent(mockFlow);
                expect.fail("Should have thrown error");
            } catch (error) {
                expect(injectionCalled).to.be.true;
                expect(error).to.be.instanceof(GameError);
            }
        });
    });

    describe("integration with current implementation patterns", () => {
        beforeEach(() => {
            injectRandomGeneratorStub.resetHistory();
        });

        it("should work similarly to other game methods like gamePlay", async () => {
            const gameError = new Error("Integration test error");
            mockGame.internalEvent.rejects(gameError);

            // Test that the error handling pattern is consistent
            let caughtError: any;
            try {
                await controller.testGameInternalEvent(mockFlow);
            } catch (error) {
                caughtError = error;
            }

            expect(caughtError).to.be.instanceof(GameError);
            expect(injectRandomGeneratorStub.calledOnce).to.be.true;
        });

        it("should validate that the method signature matches expected pattern", () => {
            const method = controller.testGameInternalEvent;
            expect(typeof method).to.equal("function");
            expect(method.length).to.equal(1); // expects one parameter
        });

        it("should verify injectRandomGenerator is called exactly once per invocation", async () => {
            const successResponse: GamePlayResponse = { request: "play", totalBet: 50, totalWin: 100 };
            mockGame.internalEvent.resolves(successResponse);

            await controller.testGameInternalEvent(mockFlow);
            await controller.testGameInternalEvent(mockFlow);

            // Should be called twice (once per invocation)
            expect(injectRandomGeneratorStub.callCount).to.equal(2);
        });
    });

    describe("method call verification", () => {
        it("should verify call order: injectRandomGenerator then game.internalEvent", async () => {
            const successResponse: GamePlayResponse = { request: "play", totalBet: 10, totalWin: 20 };
            mockGame.internalEvent.resolves(successResponse);

            const callOrder: string[] = [];

            injectRandomGeneratorStub.callsFake(() => {
                callOrder.push("inject");
            });

            mockGame.internalEvent.callsFake(() => {
                callOrder.push("internalEvent");
                return Promise.resolve(successResponse);
            });

            await controller.testGameInternalEvent(mockFlow);

            expect(callOrder).to.deep.equal(["inject", "internalEvent"]);
        });

        it("should verify that game.internalEvent receives the correct flow parameter", async () => {
            const successResponse: GamePlayResponse = { request: "play", totalBet: 15, totalWin: 30 };
            mockGame.internalEvent.resolves(successResponse);

            await controller.testGameInternalEvent(mockFlow);

            expect(mockGame.internalEvent.getCall(0).args[0]).to.equal(mockFlow);
        });
    });
});
