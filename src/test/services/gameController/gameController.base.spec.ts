import { should, use } from "chai";
import { SinonStub, stub } from "sinon";
import { GameData, getService as getAuthservice, StartGameResult } from "../../../skywind/services/auth";
import { createGameToken, flushAll, syncModels, TestLoadResult } from "../../helper";
import * as GameService from "../../../skywind/services/game/game";
import { FunStartGameToken, GameInitResponse } from "@skywind-group/sw-game-core";
import { GameTokenData, generateStartGameToken, Limits } from "../../../skywind/services/tokens";
import config from "../../../skywind/config";
import { createWalletManager, PaymentOperation } from "../../../skywind/services/wallet";
import { funDefaultLimit } from "../../funDefaultLimits";
import { TestGame } from "../../testGames";
import {
    GameHistory,
    GameInfo,
    GamePlayResponse,
    PaymentInfo,
    SomeGame
} from "@skywind-group/sw-game-core";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import { setRandomGeneratorFactory } from "../../../index";
import { RandomGeneratorFactoryImpl } from "../../testRandomFactory";
import { getMerchantKeepAliveService } from "../../../skywind/services/merchantKeepAlive";

require("source-map-support").install();

const defaultLimitsByCurrency = funDefaultLimit.byCurrencies;

should();
use(require("chai-as-promised"));

export class BaseGameControllerSpec {
    public static startGame: SinonStub;
    public static funStartGame: SinonStub;
    public static loadGame: SinonStub;
    public static keepAlive: SinonStub;
    public static startTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        providerGameCode: "test_slot",
        brandId: 1,
    };
    public static funStartGameToken: FunStartGameToken = {
        playerCode: "PL001",
        gameCode: "for_unit_tests",
        brandId: 1,
        currency: "USD"
    };
    public static funGameData: GameData = {
        gameTokenData: undefined,
        playedFromCountry: "GB",
        limits: defaultLimitsByCurrency["USD"],
        gameId: "test_slot"
    };
    public static funGameTokenData: GameTokenData = {
        playerCode: BaseGameControllerSpec.funStartGameToken.playerCode,
        gameCode: BaseGameControllerSpec.funStartGameToken.gameCode,
        brandId: BaseGameControllerSpec.funStartGameToken.brandId,
        currency: BaseGameControllerSpec.funStartGameToken.currency,
        playmode: "fun",
        providerCode: "ITG"
    };
    public static gameData: GameData = {
        gameId: "test_slot",
        gameTokenData: undefined,
    } as GameData;
    public static gameTokenData: GameTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        playmode: "real",
        providerCode: "ITG"
    };
    public static gameDataWithLimits: GameData = {
        gameTokenData: undefined,
        gameMode: "real",
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        jrsdSettings: { a: 1 },
        jurisdictionCode: "GB",
        playedFromCountry: "GB",
        gameId: "test_slot",
        renderType: 0,
        limits: {
            coins: [1],
            defaultCoin: 1,
            maxTotalStake: 500,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 0.1,
            winMax: 3000000,
            currencyMultiplier: 100,
        } as Limits
    } as GameData;
    public static gameDataWithLimitsAndPlayerInfo: GameData = {
        ...BaseGameControllerSpec.gameDataWithLimits,
        player: {
            nickname: "testNickname",
            isVip: true,
            isPrivateChatBlock: false,
            isPublicChatBlock: false,
            hasWarn: false
        },
        brandInfo: {
            name: "test",
            title: "Brand"
        }
    } as GameData;
    public static startGameResultWithLimits = {
        gameData: BaseGameControllerSpec.gameDataWithLimits,
        clientSettings: {}
    } as StartGameResult;
    public static startGameResultWithLimitsAndPlayerInfo = {
        gameData: BaseGameControllerSpec.gameDataWithLimitsAndPlayerInfo,
        clientSettings: {}
    } as StartGameResult;
    public static funStartGameResult = {
        gameData: BaseGameControllerSpec.funGameData,
        clientSettings: {}
    } as StartGameResult;
    public static startGameResult = {
        gameData: BaseGameControllerSpec.gameData,
        clientSettings: {}
    } as StartGameResult;
    public static startToken: string;
    public walletThroughAPIPrevValue;

    public static async before() {
        await syncModels();
        BaseGameControllerSpec.startGame = stub(getAuthservice("real"), "startGame");
        BaseGameControllerSpec.funStartGame = stub(getAuthservice("fun"), "startGame");
        BaseGameControllerSpec.loadGame = stub(GameService, "load");
        BaseGameControllerSpec.startToken = await generateStartGameToken(this.startTokenData);
        BaseGameControllerSpec.gameTokenData.token = await createGameToken(this.gameTokenData);
        BaseGameControllerSpec.gameData.gameTokenData = this.gameTokenData;
        BaseGameControllerSpec.funGameTokenData.token = await createGameToken(this.funGameTokenData);
        BaseGameControllerSpec.funGameData.gameTokenData = this.funGameTokenData;
        BaseGameControllerSpec.keepAlive = stub(getMerchantKeepAliveService(), "ping");
        BaseGameControllerSpec.gameDataWithLimits.gameTokenData = this.gameTokenData;
        BaseGameControllerSpec.gameDataWithLimitsAndPlayerInfo.gameTokenData = this.gameTokenData;
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
    }

    public static async after() {
        BaseGameControllerSpec.startGame.restore();
        BaseGameControllerSpec.funStartGame.restore();
        BaseGameControllerSpec.loadGame.restore();
        BaseGameControllerSpec.keepAlive.restore();
    }

    public async before(): Promise<any> {
        this.walletThroughAPIPrevValue = config.walletThroughAPI;
        config.walletThroughAPI = false;
        await getGameContextModel().truncate();
        await flushAll();

        const walletManager = createWalletManager(BaseGameControllerSpec.gameData.gameTokenData);
        const trxId = await walletManager.generateTransactionId();
        return walletManager.commitOperation({
            operation: "payment",
            transactionId: trxId,
            currency: "USD",
            bet: 0,
            win: 1000
        } as PaymentOperation);
    }

    public async after() {
        BaseGameControllerSpec.startGame.resetBehavior();
        BaseGameControllerSpec.funStartGame.resetBehavior();
        BaseGameControllerSpec.loadGame.resetBehavior();
        BaseGameControllerSpec.keepAlive.resetBehavior();
        config.walletThroughAPI = this.walletThroughAPIPrevValue;
    }

    protected stubGame(game?: SomeGame) {
        BaseGameControllerSpec.loadGame.resolves(new TestLoadResult(game || this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "defaultScene",
                    "nextScene": "nextScene",
                }
            ])));
    }

    protected createGame(initResponses: GameInitResponse[],
                         contexts: any[] = [],
                         payments: PaymentInfo[] = [],
                         histories: GameHistory[] = [],
                         playResponses: GamePlayResponse[] = [],
                         info?: () => GameInfo): SomeGame {
        return new TestGame(initResponses, contexts, payments, histories, playResponses, info);
    }
}
