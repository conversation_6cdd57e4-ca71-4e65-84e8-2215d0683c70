import { expect, should, use } from "chai";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { getService as getPlayerGameInfoService } from "../../../skywind/services/playerGameInfo";
import { suite, test, timeout } from "mocha-typescript";
import { BaseGameControllerSpec } from "./gameController.base.spec";
import { stub, SinonStub } from "sinon";
import { AsyncGameController, createAsyncGameController } from "../../../skywind/services/asynccontroller";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller: chooseGame")
export class GameControllerChooseGameSpec extends BaseGameControllerSpec {
    public asyncGameController: AsyncGameController;

    public getPlayerGameURLInfoMock: SinonStub;
    public createFlowForRequestMock: SinonStub;
    public createFlowForAsyncRequestMock: SinonStub;

    private playerGameURLInfo = {
        "token": "test",
        "url": "http://test.com"
    };

    private pushService = {
        notifyPlayer: () => Promise.resolve(),
        notifyError: () => Promise.resolve(),
        scheduleInternalEvent: () => undefined
    };

    private flowData = {
        gameData: {
            gameTokenData: {
                brandId: 1234,
                playerCode: "PlayerC",
                gameCode: "sw_kk",
                currency: "EUR"
            }
        },
        getLogData: () => "log data"
    };

    public async before() {
        this.getPlayerGameURLInfoMock = stub(getPlayerGameInfoService(), "getPlayerGameURLInfo");
        this.getPlayerGameURLInfoMock.resolves(Promise.resolve(this.playerGameURLInfo));
        // TODO: This method is ignored because this method is not public.
        // @ts-ignore
        this.createFlowForRequestMock = stub(getSyncGameController(), "createFlowForRequest");
        this.createFlowForRequestMock.returns(this.flowData);
        this.asyncGameController = createAsyncGameController(this.pushService);
        // TODO: This method is ignored because this method is not public.
        // @ts-ignore
        this.createFlowForAsyncRequestMock = stub(this.asyncGameController, "createFlowForRequest");
        this.createFlowForAsyncRequestMock.returns(this.flowData);
        return super.before();
    }

    public async after() {
        this.getPlayerGameURLInfoMock.restore();
        this.createFlowForRequestMock.restore();
        this.createFlowForAsyncRequestMock.restore();
        return super.after();
    }

    @test("processes 'chooseGame' request")
    public async processChooseGameRequest() {
        const result = await getSyncGameController().process({
            request: "choose-game",
            startGameToken: BaseGameControllerSpec.startToken,
            gameId: "sw_ff",
            requestId: 0
        });
        expect(result).to.deep.equal(this.playerGameURLInfo);
    }

    @test("processes 'chooseGame' request asynchronously")
    public async processAsyncChooseGameRequest() {
        const result = await this.asyncGameController.process({
            request: "choose-game",
            startGameToken: BaseGameControllerSpec.startToken,
            gameId: "sw_ff",
            requestId: 0
        });
        expect(result).to.deep.equal(this.playerGameURLInfo);
    }

    @test("processes 'chooseGame' request for live game")
    public async processChooseGameRequestForLiveGame() {
        const result = await getSyncGameController().process({
            request: "choose-game",
            startGameToken: BaseGameControllerSpec.startToken,
            gameId: "sw_ff",
            requestId: 0,
            isLive: true,
        });
        expect(result).to.deep.equal({
            result: {
                request: "choose-game",
                payload: this.playerGameURLInfo
            }
        });
    }

    @test("processes 'chooseGame' request asynchronously for live game")
    public async processAsyncChooseGameRequestForLiveGame() {
        const result = await this.asyncGameController.process({
            request: "choose-game",
            startGameToken: BaseGameControllerSpec.startToken,
            gameId: "sw_ff",
            requestId: 0,
            isLive: true,
        });
        expect(result).to.deep.equal({
            result: {
                request: "choose-game",
                payload: this.playerGameURLInfo
            }
        });
    }
}
