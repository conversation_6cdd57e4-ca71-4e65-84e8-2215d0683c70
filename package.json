{"name": "@skywind-group/sw-currency-exchange", "version": "2.3.20", "description": "Currency exchange service", "license": "ISC", "author": "", "main": "lib/index.js", "typings": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.js"}, "./currency": {"types": "./lib/currency.d.ts", "require": "./lib/currency.js"}, "./package.json": "./package.json"}, "scripts": {"compile": "tsc", "clean": "rm -rf ./lib", "only-test": "nyc mocha src/test/**/*.spec.ts src/test/*.spec.ts", "test": "npm run only-test && npm run sonar", "sonar": "node sonarqube.mjs", "eslint": "eslint --ext .ts src"}, "devDependencies": {"@skywind-group/sw-utils": "^2.2.0", "@types/chai": "^4.3.16", "@types/chai-as-promised": "^7.1.8", "@types/ioredis-mock": "^8.2.5", "@types/mocha": "^10.0.6", "@types/node": "^20.17.1", "@types/node-schedule": "^2.1.7", "@types/sinon": "^17.0.3", "@types/superagent": "^8.1.7", "@typescript-eslint/eslint-plugin": "^7.9.0", "@typescript-eslint/parser": "^7.9.0", "bole": "^5.0.12", "chai": "^4.4.1", "chai-as-promised": "^7.1.2 ", "eslint": "^8.57.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-sonarjs": "^1.0.3", "eslint-plugin-unicorn": "^53.0.0", "express-prom-bundle": "^7.0.0", "ioredis": "^5.4.1", "ioredis-mock": "^8.9.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mocha": "^10.4.0", "nyc": "^15.1.0", "prom-client": "~15.0.0", "reflect-metadata": "^0.2.2", "sinon": "^17.0.1", "sonarqube-scanner": "3.5.0", "superagent": "^8.1.2 || ^9.0.2", "superagent-mock": "5.0.1", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "peerDependencies": {"node-schedule": "^2.1.1", "jsonwebtoken": "^9.0.2", "superagent": "^8.1.2 || ^9.0.2 || ^10.0.0"}, "packageManager": "npm@11.0.0"}