version: '3.4'
services:
  db:
    image: 'postgres'
    command: postgres -c fsync=off -c synchronous_commit=off -c full_page_writes=off
    ports:
      - 5432
    environment:
        POSTGRES_HOST_AUTH_METHOD: trust
  redis:
    image: redis:8.0.2-alpine3.21
    command: redis-server --save "" --appendonly no
    ports:
      - 6379
  sonar:
    build:
      context: .
      dockerfile: Dockerfile.prod
    links:
      - db
      - redis
    environment:
      PGUSER: postgres
      PGDATABASE: postgres
      EVENT_CONSUMER_PGUSER: postgres
      EVENT_CONSUMER_PGDATABASE: postgres
      SYNC_ON_START: 'true'
      SONAR_HOST_URL: "${SONAR_HOST_URL}"
      REDIS_SHOW_FRIENDLY_ERROR_STACK: 'true'
    command: sh -c "npm run only-test -loglevel silent"
