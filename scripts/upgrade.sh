#!/usr/bin/env bash

branch_name="bugfix/SWS-51661-bnc-should-be-displayed-in-bo-as-fun-bonus-bnc-and-in-test-site-as-bnc-fun-bonus"
commit_message="SWS-51661: BNC should be displayed in BO as \"FUN Bonus (BNC)\" and in Test site as \"BNC :: FUN Bonus\""

V1_VERSION="1.6.19"
V2_VERSION="2.3.19"
target_branch="release/5.55"

clean_up() {
  test -d "$tmp_dir" && rm -fr "$tmp_dir"
}

upgrade() {
  git clone --branch "${target_branch}" --depth 1 "${1}"
  cd "$(basename "$1" .git)" || exit

  git checkout -b "${branch_name}"
  VERSION="${2}" node "${dir}/update-version.js"
  git commit -a -m "${commit_message}"
  git push -u origin "${branch_name}"
  cd ".." || exit
}

upgrade_v1() {
  upgrade "$1" $V1_VERSION
}

upgrade_v2() {
  upgrade "$1" $V2_VERSION
}

tmp_dir=$( mktemp -d -t my-script )
trap "clean_up $tmp_dir" EXIT

dir="$(cd -P -- "$(dirname -- "${BASH_SOURCE[0]}")" && pwd -P)"
cd "${tmp_dir}" || exit

# upgrade_v2 "ssh://******************************:7999/sbep/sw-currency-exchange-api.git"
# upgrade_v2 "ssh://******************************:7999/sbep/sw-management-api.git"
# upgrade_v2 "ssh://******************************:7999/swb/ipm-mock.git"
# upgrade_v2 "ssh://******************************:7999/swb/sw-integration-seamless.git"
# upgrade_v2 "ssh://******************************:7999/swjpn/sw-jpn-server-api.git"
# upgrade_v2 "ssh://******************************:7999/sbep/sw-slot-engine.git"

# upgrade_v1 "ssh://******************************:7999/swb/sw-integration-relax.git"
